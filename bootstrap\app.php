<?php

use App\Exceptions\Handler;
use App\Http\Middleware\HandleAppearance;
use App\Http\Middleware\HandleInertiaRequests;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: ['appearance', 'sidebar_state']);

        $middleware->web(append: [
            HandleAppearance::class,
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
            \App\Http\Middleware\AddPerformanceHeaders::class,
            // \App\Http\Middleware\CachePublicPages::class, // Disabled caching
            \App\Http\Middleware\HandleErrors::class,
        ]);

        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminAuth::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Custom error handling untuk semua request
        $exceptions->render(function (Throwable $e, $request) {
            // Gunakan custom Handler untuk render
            $handler = new \App\Exceptions\Handler(app());

            return $handler->render($request, $e);
        });
    })->create();
