<?php

use App\Models\News;
use App\Models\Potential;
use App\Models\Profile;
use App\Models\Service;

describe('Responsive Design Testing', function () {
    beforeEach(function () {
        // Create test data
        News::factory()->published()->count(5)->create();
        Service::factory()->active()->count(6)->create();
        Potential::factory()->featured()->count(4)->create();
        Profile::factory()->history()->create();
    });

    describe('Mobile Device Testing', function () {
        it('serves mobile-optimized homepage', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            ]);

            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page->component('Public/Home')
                ->has('isMobile')
                ->where('isMobile', true)
            );
        });

        it('includes mobile-specific meta tags', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('seo.viewport')
                ->where('seo.viewport', 'width=device-width, initial-scale=1.0')
            );
        });

        it('optimizes images for mobile', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->where('latestNews', function ($news) {
                foreach ($news as $article) {
                    if (isset($article['featured_image']) && $article['featured_image']) {
                        // Should include responsive image data
                        return isset($article['featured_image']['thumbnail']);
                    }
                }

                return true;
            })
            );
        });

        it('provides mobile navigation structure', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('navigation')
                ->has('navigation.mobile')
                ->where('navigation.mobile', true)
            );
        });
    });

    describe('Tablet Device Testing', function () {
        it('serves tablet-optimized layout', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            ]);

            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page->component('Public/Home')
                ->has('isTablet')
                ->where('isTablet', true)
            );
        });

        it('adjusts content layout for tablet', function () {
            $response = $this->get('/berita', [
                'User-Agent' => 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('layout')
                ->where('layout.columns', 2) // Tablet should show 2 columns
            );
        });
    });

    describe('Desktop Testing', function () {
        it('serves full desktop layout', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            ]);

            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page->component('Public/Home')
                ->has('isDesktop')
                ->where('isDesktop', true)
            );
        });

        it('shows full navigation menu on desktop', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ]);

            $response->assertInertia(fn ($page) => $page->has('navigation')
                ->has('navigation.desktop')
                ->where('navigation.desktop', true)
            );
        });
    });

    describe('Touch-Friendly Interface', function () {
        it('provides touch-friendly button sizes on mobile', function () {
            $response = $this->get('/layanan', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('touchOptimized')
                ->where('touchOptimized', true)
            );
        });

        it('optimizes form inputs for touch', function () {
            $response = $this->get('/layanan', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('formOptimization')
                ->where('formOptimization.touchFriendly', true)
            );
        });
    });

    describe('Content Adaptation', function () {
        it('adapts news list layout for mobile', function () {
            $response = $this->get('/berita', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('layout')
                ->where('layout.type', 'mobile')
                ->where('layout.columns', 1)
            );
        });

        it('shows condensed service information on mobile', function () {
            $response = $this->get('/layanan', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->where('services', function ($services) {
                foreach ($services as $service) {
                    // Mobile should show condensed info
                    return isset($service['condensed']) && $service['condensed'] === true;
                }

                return true;
            })
            );
        });

        it('adapts potential gallery for mobile', function () {
            $response = $this->get('/potensi', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('galleryLayout')
                ->where('galleryLayout.mobile', true)
            );
        });
    });

    describe('Performance on Mobile', function () {
        it('loads quickly on mobile connection', function () {
            $start = microtime(true);

            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $end = microtime(true);
            $loadTime = $end - $start;

            $response->assertStatus(200);
            expect($loadTime)->toBeLessThan(3.0); // Should load within 3 seconds on mobile
        });

        it('optimizes database queries for mobile', function () {
            \DB::enableQueryLog();

            $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $queries = \DB::getQueryLog();

            // Mobile should use efficient queries
            expect(count($queries))->toBeLessThan(8);
        });

        it('serves compressed content for mobile', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
                'Accept-Encoding' => 'gzip, deflate',
            ]);

            $response->assertStatus(200);
            // Should include compression headers
        });
    });

    describe('Accessibility on Mobile', function () {
        it('includes proper ARIA labels for mobile navigation', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('accessibility')
                ->has('accessibility.mobileNavigation')
                ->where('accessibility.mobileNavigation.ariaLabels', true)
            );
        });

        it('provides keyboard navigation support', function () {
            $response = $this->get('/layanan', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('accessibility')
                ->where('accessibility.keyboardNavigation', true)
            );
        });

        it('maintains color contrast on mobile', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('accessibility')
                ->where('accessibility.colorContrast', 'AA')
            );
        });
    });

    describe('Cross-Browser Compatibility', function () {
        it('works on Safari mobile', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            ]);

            $response->assertStatus(200);
        });

        it('works on Chrome mobile', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            ]);

            $response->assertStatus(200);
        });

        it('works on Firefox mobile', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (Mobile; rv:68.0) Gecko/68.0 Firefox/68.0',
            ]);

            $response->assertStatus(200);
        });

        it('works on Edge mobile', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (Linux; Android 10; HD1913) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36 EdgA/46.3.4.5155',
            ]);

            $response->assertStatus(200);
        });
    });

    describe('Viewport and Scaling', function () {
        it('prevents horizontal scrolling on mobile', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('seo.viewport')
                ->where('seo.viewport', fn ($viewport) => str_contains($viewport, 'width=device-width') &&
                    str_contains($viewport, 'initial-scale=1.0')
                )
            );
        });

        it('handles zoom properly on mobile', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->has('seo.viewport')
                ->where('seo.viewport', fn ($viewport) => ! str_contains($viewport, 'user-scalable=no') // Should allow zooming for accessibility
                )
            );
        });
    });

    describe('Image Optimization', function () {
        it('serves appropriate image sizes for different devices', function () {
            $mobileResponse = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $desktopResponse = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ]);

            $mobileResponse->assertInertia(fn ($page) => $page->has('imageOptimization')
                ->where('imageOptimization.mobile', true)
            );

            $desktopResponse->assertInertia(fn ($page) => $page->has('imageOptimization')
                ->where('imageOptimization.desktop', true)
            );
        });

        it('includes srcset for responsive images', function () {
            $response = $this->get('/', [
                'User-Agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
            ]);

            $response->assertInertia(fn ($page) => $page->where('latestNews', function ($news) {
                foreach ($news as $article) {
                    if (isset($article['featured_image']) && $article['featured_image']) {
                        return isset($article['featured_image']['srcset']);
                    }
                }

                return true;
            })
            );
        });
    });
});
