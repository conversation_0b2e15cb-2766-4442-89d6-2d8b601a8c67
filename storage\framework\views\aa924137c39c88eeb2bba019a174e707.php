<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengaduan Baru - <?php echo e($complaint->ticket_number); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 20px;
            border: 1px solid #e2e8f0;
        }
        .footer {
            background-color: #64748b;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 14px;
        }
        .info-box {
            background-color: white;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            background-color: #fef3c7;
            color: #92400e;
        }
        .priority-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .priority-medium {
            background-color: #fef3c7;
            color: #92400e;
        }
        .priority-high {
            background-color: #fed7aa;
            color: #c2410c;
        }
        .priority-urgent {
            background-color: #fecaca;
            color: #dc2626;
        }
        .priority-low {
            background-color: #dcfce7;
            color: #166534;
        }
    </style>
</head>
<body>
<?php
    $villageProfile = \App\Models\Setting::get('village.profile', [
        'name' => 'Desa Lemah Duhur',
        'district' => 'Kecamatan Caringin',
        'regency' => 'Kabupaten Bogor',
        'province' => 'Jawa Barat'
    ]);
    $contactInfo = \App\Models\Setting::get('village.contact_info', [
        'email' => '<EMAIL>',
        'phone' => '(021) 8750-xxxx'
    ]);
?>

    <div class="header">
        <h1>🏛️ Pemerintah <?php echo e($villageProfile['name']); ?></h1>
        <h2>Pengaduan Baru Diterima</h2>
    </div>

    <div class="content">
        <p><strong>Kepada Tim Admin <?php echo e($villageProfile['name']); ?>,</strong></p>
        
        <p>Pengaduan baru telah diterima melalui website desa dengan detail sebagai berikut:</p>

        <div class="info-box">
            <h3>📋 Informasi Pengaduan</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 30%;">Nomor Tiket:</td>
                    <td style="padding: 8px 0; font-family: monospace; font-weight: bold;"><?php echo e($complaint->ticket_number); ?></td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Tanggal:</td>
                    <td style="padding: 8px 0;"><?php echo e($complaint->created_at->format('d F Y, H:i')); ?> WIB</td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Status:</td>
                    <td style="padding: 8px 0;">
                        <span class="status-badge"><?php echo e($complaint->status); ?></span>
                    </td>
                </tr>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Prioritas:</td>
                    <td style="padding: 8px 0;">
                        <span class="priority-badge priority-<?php echo e($complaint->priority); ?>"><?php echo e($complaint->priority); ?></span>
                    </td>
                </tr>
            </table>
        </div>

        <div class="info-box">
            <h3>👤 Data Pengadu</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 30%;">Nama:</td>
                    <td style="padding: 8px 0;"><?php echo e($complaint->name); ?></td>
                </tr>
                <?php if($complaint->email): ?>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Email:</td>
                    <td style="padding: 8px 0;"><?php echo e($complaint->email); ?></td>
                </tr>
                <?php endif; ?>
                <?php if($complaint->phone): ?>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Telepon:</td>
                    <td style="padding: 8px 0;"><?php echo e($complaint->phone); ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Kategori:</td>
                    <td style="padding: 8px 0;"><?php echo e($complaint->category); ?></td>
                </tr>
            </table>
        </div>

        <div class="info-box">
            <h3>📝 Detail Pengaduan</h3>
            <p><strong>Subjek:</strong> <?php echo e($complaint->subject); ?></p>
            <p><strong>Deskripsi:</strong></p>
            <div style="background-color: #f1f5f9; padding: 15px; border-radius: 6px; white-space: pre-wrap;"><?php echo e($complaint->description); ?></div>
            
            <?php if($complaint->attachments && count($complaint->attachments) > 0): ?>
            <p><strong>Lampiran:</strong> <?php echo e(count($complaint->attachments)); ?> file</p>
            <ul>
                <?php $__currentLoopData = $complaint->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($attachment['original_name']); ?> (<?php echo e(number_format($attachment['size'] / 1024, 1)); ?> KB)</li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <?php endif; ?>
        </div>

        <div style="background-color: #dbeafe; border: 1px solid #3b82f6; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #1e40af;">⏰ Tindak Lanjut Diperlukan</h4>
            <p style="margin: 0;">
                Harap segera tindaklanjuti pengaduan ini sesuai dengan SOP yang berlaku. 
                Target waktu respon: <strong>3-7 hari kerja</strong>.
            </p>
        </div>

        <p style="text-align: center; margin-top: 30px;">
            <a href="<?php echo e(route('admin.dashboard')); ?>" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                🔗 Buka Dashboard Admin
            </a>
        </p>
    </div>

    <div class="footer">
        <p><strong>Pemerintah <?php echo e($villageProfile['name']); ?></strong></p>
        <p><?php echo e($villageProfile['district']); ?>, <?php echo e($villageProfile['regency']); ?>, <?php echo e($villageProfile['province']); ?></p>
        <p>Email: <?php echo e($contactInfo['email']); ?> | Telepon: <?php echo e($contactInfo['phone']); ?></p>
        <p style="font-size: 12px; margin-top: 10px;">
            Email ini dikirim secara otomatis oleh sistem. Mohon tidak membalas email ini.
        </p>
    </div>
</body>
</html><?php /**PATH E:\Coding\PHP\Laravel\desa-lemah-duhur\resources\views/emails/complaint-submitted.blade.php ENDPATH**/ ?>