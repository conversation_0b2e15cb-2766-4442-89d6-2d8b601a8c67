import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AdminLayout from '@/layouts/AdminLayout';
import { Head, Link, router } from '@inertiajs/react';
// Using native date formatting instead of date-fns
import { AlertTriangle, Calendar, CheckCircle, Clock, Download, FileText, Filter, Mail, MessageSquare, Phone, Search, XCircle } from 'lucide-react';
import React, { useState } from 'react';

interface Complaint {
    id: number;
    ticket_number: string;
    name: string;
    email: string;
    phone: string;
    category: string;
    subject: string;
    description: string;
    status: string;

    priority: string;
    visibility: string;
    admin_response: string | null;
    created_at: string;
    responded_at: string | null;
    responded_by: {
        name: string;
    } | null;
}

interface Stats {
    total: number;
    pending: number;
    in_progress: number;
    resolved: number;
    closed: number;
    this_month: number;
    urgent: number;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

interface Props {
    complaints: {
        data?: Complaint[];
        links?: PaginationLink[];
        meta?: PaginationMeta;
    };
    stats: Stats;
    filters: {
        status?: string;
        category?: string;
        priority?: string;
        visibility?: string;
        search?: string;
        date_from?: string;
        date_to?: string;
    };
    categories: Record<string, string>;
    statuses: Record<string, string>;
    priorities: Record<string, string>;
    visibilities: Record<string, string>;
}

export default function ComplaintsIndex({ complaints, stats, filters, categories, statuses, priorities, visibilities }: Props) {
    const [selectedComplaints, setSelectedComplaints] = useState<number[]>([]);
    const [bulkAction, setBulkAction] = useState('');
    const [bulkValue, setBulkValue] = useState('');

    const handleFilter = (key: string, value: string) => {
        router.get(
            route('admin.complaints.index'),
            {
                ...filters,
                [key]: value === 'all' ? undefined : value,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const search = formData.get('search') as string;

        router.get(
            route('admin.complaints.index'),
            {
                ...filters,
                search: search || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedComplaints(complaints.data?.map((c) => c.id) || []);
        } else {
            setSelectedComplaints([]);
        }
    };

    const handleSelectComplaint = (id: number, checked: boolean) => {
        if (checked) {
            setSelectedComplaints([...selectedComplaints, id]);
        } else {
            setSelectedComplaints(selectedComplaints.filter((cId) => cId !== id));
        }
    };

    const handleBulkUpdate = () => {
        if (selectedComplaints.length === 0 || !bulkAction) return;

        const requestData = {
            complaint_ids: selectedComplaints,
            action: bulkAction,
            ...(bulkAction === 'status' && { status: bulkValue }),
            ...(bulkAction === 'priority' && { priority: bulkValue }),
        };

        router.post(route('admin.complaints.bulk-update'), requestData, {
            onSuccess: () => {
                setSelectedComplaints([]);
                setBulkAction('');
                setBulkValue('');
            },
        });
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-4 w-4" />;
            case 'in_progress':
                return <MessageSquare className="h-4 w-4" />;
            case 'resolved':
                return <CheckCircle className="h-4 w-4" />;
            case 'closed':
                return <XCircle className="h-4 w-4" />;
            default:
                return <Clock className="h-4 w-4" />;
        }
    };

    const getPriorityIcon = (priority: string) => {
        if (priority === 'urgent') {
            return <AlertTriangle className="h-4 w-4" />;
        }
        return null;
    };

    return (
        <AdminLayout>
            <Head title="Kelola Pengaduan" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Kelola Pengaduan</h1>
                        <p className="text-gray-600 dark:text-gray-400">Kelola pengaduan dan aspirasi masyarakat</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={() => router.get(route('admin.complaints.export.excel', filters))}>
                            <Download className="mr-2 h-4 w-4" />
                            Export Excel
                        </Button>
                        <Button variant="outline" onClick={() => router.get(route('admin.complaints.export.pdf', filters))}>
                            <FileText className="mr-2 h-4 w-4" />
                            Export PDF
                        </Button>
                    </div>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4 lg:grid-cols-7">
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-gray-600">Total</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-yellow-600">Menunggu</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-blue-600">Diproses</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-600">{stats.in_progress}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-green-600">Selesai</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stats.resolved}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-gray-600">Ditutup</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-gray-600">{stats.closed}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-blue-600">Bulan Ini</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-600">{stats.this_month}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium text-red-600">Mendesak</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{stats.urgent}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filter & Pencarian
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <form onSubmit={handleSearch} className="flex gap-2">
                            <div className="flex-1">
                                <Input
                                    name="search"
                                    placeholder="Cari berdasarkan nomor tiket, nama, atau subjek..."
                                    defaultValue={filters.search}
                                    className="w-full"
                                />
                            </div>
                            <Button type="submit">
                                <Search className="mr-2 h-4 w-4" />
                                Cari
                            </Button>
                        </form>

                        <div className="grid grid-cols-1 gap-4 md:grid-cols-6">
                            <Select value={filters.status || 'all'} onValueChange={(value) => handleFilter('status', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Semua Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Semua Status</SelectItem>
                                    {Object.entries(statuses).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>
                                            {label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={filters.category || 'all'} onValueChange={(value) => handleFilter('category', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Semua Kategori" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Semua Kategori</SelectItem>
                                    {Object.entries(categories).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>
                                            {label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={filters.priority || 'all'} onValueChange={(value) => handleFilter('priority', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Semua Prioritas" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Semua Prioritas</SelectItem>
                                    {Object.entries(priorities).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>
                                            {label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={filters.visibility || 'all'} onValueChange={(value) => handleFilter('visibility', value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Semua Visibilitas" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Semua Visibilitas</SelectItem>
                                    {Object.entries(visibilities).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>
                                            {label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Input
                                type="date"
                                placeholder="Dari Tanggal"
                                value={filters.date_from || ''}
                                onChange={(e) => handleFilter('date_from', e.target.value)}
                            />

                            <Input
                                type="date"
                                placeholder="Sampai Tanggal"
                                value={filters.date_to || ''}
                                onChange={(e) => handleFilter('date_to', e.target.value)}
                            />
                        </div>
                    </CardContent>
                </Card>

                {/* Bulk Actions */}
                {selectedComplaints.length > 0 && (
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center gap-4">
                                <span className="text-sm text-gray-600">{selectedComplaints.length} pengaduan dipilih</span>
                                <Select value={bulkAction} onValueChange={setBulkAction}>
                                    <SelectTrigger className="w-48">
                                        <SelectValue placeholder="Pilih Aksi" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="status">Update Status</SelectItem>
                                        <SelectItem value="priority">Update Prioritas</SelectItem>
                                        <SelectItem value="delete">Hapus</SelectItem>
                                    </SelectContent>
                                </Select>

                                {bulkAction === 'status' && (
                                    <Select value={bulkValue} onValueChange={setBulkValue}>
                                        <SelectTrigger className="w-48">
                                            <SelectValue placeholder="Pilih Status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(statuses).map(([key, label]) => (
                                                <SelectItem key={key} value={key}>
                                                    {label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                )}

                                {bulkAction === 'priority' && (
                                    <Select value={bulkValue} onValueChange={setBulkValue}>
                                        <SelectTrigger className="w-48">
                                            <SelectValue placeholder="Pilih Prioritas" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.entries(priorities).map(([key, label]) => (
                                                <SelectItem key={key} value={key}>
                                                    {label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                )}

                                <Button onClick={handleBulkUpdate} disabled={!bulkAction || (bulkAction !== 'delete' && !bulkValue)}>
                                    Terapkan
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Complaints List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Daftar Pengaduan</CardTitle>
                        <CardDescription>
                            Menampilkan {complaints.data?.length || 0} dari {complaints.meta?.total || 0} pengaduan
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Table Header */}
                            <div className="flex items-center gap-4 rounded-lg bg-gray-50 p-4 text-sm font-medium dark:bg-gray-800">
                                <Checkbox
                                    checked={selectedComplaints.length === (complaints.data?.length || 0) && (complaints.data?.length || 0) > 0}
                                    onCheckedChange={handleSelectAll}
                                />
                                <div className="grid flex-1 grid-cols-12 gap-4">
                                    <div className="col-span-2">Tiket</div>
                                    <div className="col-span-2">Nama</div>
                                    <div className="col-span-2">Kategori</div>
                                    <div className="col-span-2">Subjek</div>
                                    <div className="col-span-1">Status</div>
                                    <div className="col-span-1">Prioritas</div>
                                    <div className="col-span-1">Visibilitas</div>
                                    <div className="col-span-1">Tanggal</div>
                                </div>
                            </div>

                            {/* Complaints */}
                            {complaints.data?.map((complaint) => (
                                <div
                                    key={complaint.id}
                                    className="flex items-center gap-4 rounded-lg border border-gray-200 p-4 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
                                >
                                    <Checkbox
                                        checked={selectedComplaints.includes(complaint.id)}
                                        onCheckedChange={(checked) => handleSelectComplaint(complaint.id, checked as boolean)}
                                    />
                                    <div className="grid flex-1 grid-cols-12 items-center gap-4">
                                        <div className="col-span-2">
                                            <Link
                                                href={route('admin.complaints.show', complaint.id)}
                                                className="font-mono text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                            >
                                                {complaint.ticket_number}
                                            </Link>
                                        </div>
                                        <div className="col-span-2">
                                            <div className="font-medium">{complaint.name}</div>
                                            <div className="flex items-center gap-1 text-sm text-gray-500">
                                                {complaint.email && <Mail className="h-3 w-3 text-gray-500 dark:text-gray-400" />}
                                                {complaint.phone && <Phone className="h-3 w-3 text-gray-500 dark:text-gray-400" />}
                                            </div>
                                        </div>
                                        <div className="col-span-2">
                                            <Badge variant="outline">{complaint.category}</Badge>
                                        </div>
                                        <div className="col-span-2">
                                            <div className="truncate font-medium">{complaint.subject}</div>
                                            <div className="truncate text-sm text-gray-500 dark:text-gray-400">{complaint.description}</div>
                                        </div>
                                        <div className="col-span-1">
                                            <Badge
                                                variant={complaint.status === 'resolved' ? 'default' : 'secondary'}
                                                className={`flex items-center gap-1 ${
                                                    complaint.status === 'pending'
                                                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                                                        : complaint.status === 'in_progress'
                                                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                                                          : complaint.status === 'resolved'
                                                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                                            : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                                                }`}
                                            >
                                                {getStatusIcon(complaint.status)}
                                                {complaint.status}
                                            </Badge>
                                        </div>
                                        <div className="col-span-1">
                                            <Badge
                                                variant="outline"
                                                className={`flex items-center gap-1 ${
                                                    complaint.priority === 'urgent'
                                                        ? 'border-red-200 text-red-800 dark:border-red-700 dark:text-red-300'
                                                        : complaint.priority === 'high'
                                                          ? 'border-orange-200 text-orange-800 dark:border-orange-700 dark:text-orange-300'
                                                          : complaint.priority === 'medium'
                                                            ? 'border-yellow-200 text-yellow-800 dark:border-yellow-700 dark:text-yellow-300'
                                                            : 'border-green-200 text-green-800 dark:border-green-700 dark:text-green-300'
                                                }`}
                                            >
                                                {getPriorityIcon(complaint.priority)}
                                                {complaint.priority}
                                            </Badge>
                                        </div>
                                        <div className="col-span-1">
                                            <Badge
                                                variant={complaint.visibility === 'public' ? 'default' : 'secondary'}
                                                className={`${
                                                    complaint.visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                                                }`}
                                            >
                                                {complaint.visibility}
                                            </Badge>
                                        </div>
                                        <div className="col-span-1">
                                            <div className="flex items-center gap-1 text-sm text-gray-500">
                                                <Calendar className="h-3 w-3" />
                                                {new Date(complaint.created_at).toLocaleDateString('id-ID', { day: '2-digit', month: '2-digit' })}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}

                            {(complaints.data?.length || 0) === 0 && (
                                <div className="py-8 text-center text-gray-500">Tidak ada pengaduan yang ditemukan</div>
                            )}
                        </div>

                        {/* Pagination */}
                        {(complaints.meta?.last_page || 0) > 1 && (
                            <div className="mt-6 flex justify-center">
                                <div className="flex gap-2">
                                    {complaints.links?.map((link, index) => (
                                        <Link
                                            key={index}
                                            href={link.url || '#'}
                                            preserveState
                                            preserveScroll
                                            className={`rounded-md border px-3 py-1 text-sm ${
                                                link.active
                                                    ? 'border-blue-600 bg-blue-600 text-white'
                                                    : link.url
                                                      ? 'hover:bg-gray-100 dark:hover:bg-gray-700'
                                                      : 'cursor-not-allowed opacity-50'
                                            }`}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
