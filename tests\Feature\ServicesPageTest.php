<?php

use App\Models\Service;

describe('Services Page Feature', function () {
    beforeEach(function () {
        // Create test services
        Service::factory()->active()->create([
            'name' => 'Surat Keterangan Domisili',
            'description' => 'Layanan pembuatan surat keterangan domisili',
            'cost' => 0,
            'processing_time' => '1 hari',
        ]);

        Service::factory()->active()->create([
            'name' => 'Surat Keterangan Tidak Mampu',
            'description' => 'Layanan pembuatan surat keterangan tidak mampu',
            'cost' => 5000,
            'processing_time' => '2-3 hari',
        ]);

        Service::factory()->inactive()->create([
            'name' => 'Layanan Tidak Aktif',
            'description' => 'Layanan yang sedang tidak tersedia',
        ]);
    });

    it('displays services page successfully', function () {
        $response = $this->get('/layanan');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/Services/Index')
        );
    });

    it('shows list of active services', function () {
        $response = $this->get('/layanan');

        $response->assertInertia(fn ($page) => $page->has('services')
            ->where('services', fn ($services) => count($services) === 2 // Only active services
            )
        );
    });

    it('does not show inactive services', function () {
        $response = $this->get('/layanan');

        $response->assertInertia(fn ($page) => $page->where('services', function ($services) {
            foreach ($services as $service) {
                if ($service['name'] === 'Layanan Tidak Aktif') {
                    return false;
                }
            }

            return true;
        })
        );
    });

    it('displays service details correctly', function () {
        $response = $this->get('/layanan');

        $response->assertInertia(fn ($page) => $page->where('services', function ($services) {
            foreach ($services as $service) {
                if ($service['name'] === 'Surat Keterangan Domisili') {
                    return isset($service['description']) &&
                           isset($service['cost']) &&
                           isset($service['processing_time']);
                }
            }

            return false;
        })
        );
    });

    it('formats cost correctly', function () {
        $response = $this->get('/layanan');

        $response->assertInertia(fn ($page) => $page->where('services', function ($services) {
            foreach ($services as $service) {
                if ($service['name'] === 'Surat Keterangan Domisili') {
                    return $service['formatted_cost'] === 'Gratis';
                }
                if ($service['name'] === 'Surat Keterangan Tidak Mampu') {
                    return $service['formatted_cost'] === 'Rp 5.000';
                }
            }

            return false;
        })
        );
    });

    it('includes requirements and procedure', function () {
        $response = $this->get('/layanan');

        $response->assertInertia(fn ($page) => $page->where('services', function ($services) {
            foreach ($services as $service) {
                if (! isset($service['requirements_list']) ||
                    ! isset($service['procedure_list'])) {
                    return false;
                }
            }

            return true;
        })
        );
    });

    it('shows contact information', function () {
        $response = $this->get('/layanan');

        $response->assertInertia(fn ($page) => $page->has('contactInfo')
        );
    });

    it('includes operating hours', function () {
        $response = $this->get('/layanan');

        $response->assertInertia(fn ($page) => $page->has('operatingHours')
        );
    });

    it('includes SEO meta tags', function () {
        $response = $this->get('/layanan');

        $response->assertInertia(fn ($page) => $page->has('seo')
            ->has('seo.title')
            ->has('seo.description')
            ->where('seo.title', fn ($title) => str_contains($title, 'Layanan')
            )
        );
    });

    it('handles empty services gracefully', function () {
        Service::query()->delete();

        $response = $this->get('/layanan');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/Services/Index')
            ->has('services')
            ->where('services', fn ($services) => count($services) === 0
            )
        );
    });

    describe('Service Detail Modal/Page', function () {
        it('can view individual service details', function () {
            $service = Service::factory()->active()->create([
                'name' => 'Test Service',
                'requirements' => ['KTP', 'KK'],
                'procedure' => ['Step 1', 'Step 2'],
            ]);

            $response = $this->get("/layanan/{$service->id}");

            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page->component('Public/Services/Detail')
                ->has('service')
                ->where('service.name', 'Test Service')
            );
        });

        it('shows 404 for inactive service', function () {
            $service = Service::factory()->inactive()->create();

            $response = $this->get("/layanan/{$service->id}");

            $response->assertStatus(404);
        });

        it('shows 404 for non-existent service', function () {
            $response = $this->get('/layanan/999');

            $response->assertStatus(404);
        });
    });

    describe('Search and Filter', function () {
        it('can search services by name', function () {
            $response = $this->get('/layanan?search=domisili');

            $response->assertInertia(fn ($page) => $page->where('services', function ($services) {
                foreach ($services as $service) {
                    if (! str_contains(strtolower($service['name']), 'domisili')) {
                        return false;
                    }
                }

                return true;
            })
            );
        });

        it('returns empty results for non-matching search', function () {
            $response = $this->get('/layanan?search=nonexistent');

            $response->assertInertia(fn ($page) => $page->where('services', fn ($services) => count($services) === 0
            )
            );
        });
    });

    describe('Performance', function () {
        it('loads services efficiently', function () {
            \DB::enableQueryLog();

            $response = $this->get('/layanan');

            $queries = \DB::getQueryLog();

            $response->assertStatus(200);
            expect(count($queries))->toBeLessThan(5);
        });
    });

    describe('Content Validation', function () {
        it('contains Indonesian service names', function () {
            $response = $this->get('/layanan');

            $response->assertInertia(fn ($page) => $page->where('services', function ($services) {
                foreach ($services as $service) {
                    if (str_contains($service['name'], 'Surat')) {
                        return true;
                    }
                }

                return false;
            })
            );
        });
    });
});
