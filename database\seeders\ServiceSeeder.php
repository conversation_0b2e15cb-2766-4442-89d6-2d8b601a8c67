<?php

namespace Database\Seeders;

use App\Models\Service;
use Illuminate\Database\Seeder;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $servicesData = [
            [
                'name' => 'Surat Keterangan Domisili',
                'description' => 'Layanan pembuatan surat keterangan domisili untuk warga yang membutuhkan bukti tempat tinggal.',
                'requirements' => [
                    'Fotocopy KTP yang masih berlaku',
                    'Fotocopy Kartu Keluarga',
                    'Surat pengantar dari RT/RW',
                    'Pas foto 3x4 sebanyak 2 lembar',
                ],
                'procedure' => [
                    'Datang ke kantor desa dengan membawa persyaratan lengkap',
                    'Mengisi formulir permohonan surat keterangan domisili',
                    'Menyerahkan berkas persyaratan ke petugas',
                    'Menunggu proses verifikasi data',
                    'Surat dapat diambil sesuai jadwal yang ditentukan',
                ],
                'cost' => '5000',
                'processing_time' => '1-2 hari kerja',
                'contact_info' => [
                    'phone' => '0293-123456',
                    'whatsapp' => '081234567890',
                    'email' => '<EMAIL>',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Surat Keterangan Tidak Mampu (SKTM)',
                'description' => 'Layanan pembuatan surat keterangan tidak mampu untuk keperluan bantuan sosial, beasiswa, atau keringanan biaya.',
                'requirements' => [
                    'Fotocopy KTP yang masih berlaku',
                    'Fotocopy Kartu Keluarga',
                    'Surat pengantar dari RT/RW',
                    'Surat keterangan penghasilan dari tempat kerja (jika ada)',
                    'Pas foto 3x4 sebanyak 2 lembar',
                ],
                'procedure' => [
                    'Datang ke kantor desa dengan membawa persyaratan lengkap',
                    'Mengisi formulir permohonan SKTM',
                    'Menyerahkan berkas persyaratan ke petugas',
                    'Petugas akan melakukan survei ke rumah pemohon',
                    'Surat dapat diambil setelah survei selesai',
                ],
                'cost' => '0',
                'processing_time' => '3-5 hari kerja',
                'contact_info' => [
                    'phone' => '0293-123456',
                    'whatsapp' => '081234567890',
                    'email' => '<EMAIL>',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Surat Pengantar KTP/KK',
                'description' => 'Layanan pembuatan surat pengantar untuk keperluan pembuatan atau perpanjangan KTP dan Kartu Keluarga.',
                'requirements' => [
                    'Fotocopy KTP lama (untuk perpanjangan)',
                    'Fotocopy Kartu Keluarga',
                    'Surat pengantar dari RT/RW',
                    'Pas foto 3x4 sebanyak 4 lembar',
                    'Akta kelahiran (untuk KTP baru)',
                ],
                'procedure' => [
                    'Datang ke kantor desa dengan membawa persyaratan lengkap',
                    'Mengisi formulir permohonan surat pengantar',
                    'Menyerahkan berkas persyaratan ke petugas',
                    'Petugas akan memverifikasi data',
                    'Surat pengantar dapat diambil pada hari yang sama',
                ],
                'cost' => '3000',
                'processing_time' => '1 hari kerja',
                'contact_info' => [
                    'phone' => '0293-123456',
                    'whatsapp' => '081234567890',
                    'email' => '<EMAIL>',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Surat Keterangan Usaha',
                'description' => 'Layanan pembuatan surat keterangan usaha untuk keperluan pengajuan kredit, tender, atau legalitas usaha.',
                'requirements' => [
                    'Fotocopy KTP yang masih berlaku',
                    'Fotocopy Kartu Keluarga',
                    'Surat pengantar dari RT/RW',
                    'Foto tempat usaha',
                    'Keterangan jenis usaha dan omzet',
                ],
                'procedure' => [
                    'Datang ke kantor desa dengan membawa persyaratan lengkap',
                    'Mengisi formulir permohonan surat keterangan usaha',
                    'Menyerahkan berkas persyaratan ke petugas',
                    'Petugas akan melakukan survei ke lokasi usaha',
                    'Surat dapat diambil setelah survei selesai',
                ],
                'cost' => '10000',
                'processing_time' => '2-3 hari kerja',
                'contact_info' => [
                    'phone' => '0293-123456',
                    'whatsapp' => '081234567890',
                    'email' => '<EMAIL>',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Surat Keterangan Kelahiran',
                'description' => 'Layanan pembuatan surat keterangan kelahiran untuk keperluan pembuatan akta kelahiran di Disdukcapil.',
                'requirements' => [
                    'Surat keterangan lahir dari bidan/dokter',
                    'Fotocopy KTP kedua orang tua',
                    'Fotocopy Kartu Keluarga',
                    'Fotocopy buku nikah orang tua',
                    'Surat pengantar dari RT/RW',
                ],
                'procedure' => [
                    'Datang ke kantor desa dengan membawa persyaratan lengkap',
                    'Mengisi formulir permohonan surat keterangan kelahiran',
                    'Menyerahkan berkas persyaratan ke petugas',
                    'Petugas akan memverifikasi data',
                    'Surat dapat diambil pada hari yang sama',
                ],
                'cost' => '5000',
                'processing_time' => '1 hari kerja',
                'contact_info' => [
                    'phone' => '0293-123456',
                    'whatsapp' => '081234567890',
                    'email' => '<EMAIL>',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Surat Keterangan Kematian',
                'description' => 'Layanan pembuatan surat keterangan kematian untuk keperluan administrasi dan pembuatan akta kematian.',
                'requirements' => [
                    'Surat keterangan kematian dari dokter/rumah sakit',
                    'Fotocopy KTP almarhum',
                    'Fotocopy Kartu Keluarga',
                    'Fotocopy KTP pelapor',
                    'Surat pengantar dari RT/RW',
                ],
                'procedure' => [
                    'Datang ke kantor desa dengan membawa persyaratan lengkap',
                    'Mengisi formulir permohonan surat keterangan kematian',
                    'Menyerahkan berkas persyaratan ke petugas',
                    'Petugas akan memverifikasi data',
                    'Surat dapat diambil pada hari yang sama',
                ],
                'cost' => '5000',
                'processing_time' => '1 hari kerja',
                'contact_info' => [
                    'phone' => '0293-123456',
                    'whatsapp' => '081234567890',
                    'email' => '<EMAIL>',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Surat Keterangan Pindah',
                'description' => 'Layanan pembuatan surat keterangan pindah untuk warga yang akan pindah domisili ke daerah lain.',
                'requirements' => [
                    'Fotocopy KTP yang masih berlaku',
                    'Fotocopy Kartu Keluarga',
                    'Surat pengantar dari RT/RW',
                    'Surat keterangan dari tempat tujuan (jika ada)',
                    'Pas foto 3x4 sebanyak 2 lembar',
                ],
                'procedure' => [
                    'Datang ke kantor desa dengan membawa persyaratan lengkap',
                    'Mengisi formulir permohonan surat keterangan pindah',
                    'Menyerahkan berkas persyaratan ke petugas',
                    'Petugas akan memverifikasi data dan alamat tujuan',
                    'Surat dapat diambil setelah verifikasi selesai',
                ],
                'cost' => '7500',
                'processing_time' => '1-2 hari kerja',
                'contact_info' => [
                    'phone' => '0293-123456',
                    'whatsapp' => '081234567890',
                    'email' => '<EMAIL>',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Surat Izin Keramaian',
                'description' => 'Layanan pembuatan surat izin keramaian untuk keperluan acara atau kegiatan yang melibatkan banyak orang.',
                'requirements' => [
                    'Fotocopy KTP penanggung jawab acara',
                    'Proposal kegiatan',
                    'Surat pengantar dari RT/RW',
                    'Surat pernyataan bertanggung jawab',
                    'Denah lokasi acara',
                ],
                'procedure' => [
                    'Datang ke kantor desa dengan membawa persyaratan lengkap',
                    'Mengisi formulir permohonan izin keramaian',
                    'Menyerahkan berkas persyaratan ke petugas',
                    'Petugas akan meninjau proposal dan lokasi',
                    'Surat izin dapat diambil setelah persetujuan',
                ],
                'cost' => '15000',
                'processing_time' => '3-5 hari kerja',
                'contact_info' => [
                    'phone' => '0293-123456',
                    'whatsapp' => '081234567890',
                    'email' => '<EMAIL>',
                ],
                'is_active' => true,
            ],
        ];

        foreach ($servicesData as $service) {
            Service::create($service);
        }
    }
}
