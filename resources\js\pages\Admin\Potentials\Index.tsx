import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem, type PaginatedData } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Edit, Eye, MapPin, Plus, Search, Star, StarOff, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Potential {
    id: number;
    name: string;
    type: 'tourism' | 'umkm';
    description: string;
    location: string | null;
    is_featured: boolean;
    images: string[];
    created_at: string;
    updated_at: string;
}

interface Props {
    potentials: PaginatedData<Potential>;
    filters: {
        type?: string;
        featured?: string;
        search?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Potensi',
        href: '/admin/potentials',
    },
];

export default function PotentialsIndex({ potentials, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const { delete: destroy } = useForm();

    // Handle search with debounce
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchTerm !== filters.search) {
                router.get(
                    '/admin/potentials',
                    {
                        type: filters.type,
                        featured: filters.featured,
                        search: searchTerm || undefined,
                    },
                    {
                        preserveState: true,
                        replace: true,
                    },
                );
            }
        }, 300);

        return () => clearTimeout(timeoutId);
    }, [searchTerm, filters.search, filters.type, filters.featured]);

    const handleFilterChange = (key: string, value: string) => {
        router.get(
            '/admin/potentials',
            {
                ...filters,
                [key]: value || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDelete = (potential: Potential) => {
        if (confirm(`Apakah Anda yakin ingin menghapus potensi "${potential.name}"?`)) {
            destroy(route('admin.potentials.destroy', potential.id), {
                preserveScroll: true,
            });
        }
    };

    const handleToggleFeatured = (potential: Potential) => {
        const action = potential.is_featured ? 'hapus dari unggulan' : 'jadikan unggulan';
        if (confirm(`Apakah Anda yakin ingin ${action} potensi "${potential.name}"?`)) {
            router.post(
                route('admin.potentials.toggle-featured', potential.id),
                {},
                {
                    preserveScroll: true,
                },
            );
        }
    };

    const getTypeLabel = (type: string) => {
        return type === 'tourism' ? 'Wisata' : 'UMKM';
    };

    const getTypeColor = (type: string) => {
        return type === 'tourism' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800';
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Manajemen Potensi - Admin Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Manajemen Potensi</h1>
                        <p className="text-gray-600 dark:text-gray-300">Kelola potensi wisata dan UMKM desa</p>
                    </div>
                    <Link href={route('admin.potentials.create')}>
                        <Button className="flex items-center space-x-2">
                            <Plus className="h-4 w-4" />
                            <span>Tambah Potensi</span>
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Search className="h-5 w-5" />
                            <span>Filter & Pencarian</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Pencarian</label>
                                <Input
                                    placeholder="Cari nama, deskripsi, atau lokasi..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Tipe</label>
                                <Select
                                    value={filters.type || 'all'}
                                    onValueChange={(value) => handleFilterChange('type', value === 'all' ? '' : value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Tipe" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Tipe</SelectItem>
                                        <SelectItem value="tourism">Wisata</SelectItem>
                                        <SelectItem value="umkm">UMKM</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Status Unggulan</label>
                                <Select
                                    value={filters.featured || 'all'}
                                    onValueChange={(value) => handleFilterChange('featured', value === 'all' ? '' : value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Status</SelectItem>
                                        <SelectItem value="yes">Unggulan</SelectItem>
                                        <SelectItem value="no">Bukan Unggulan</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-end">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setSearchTerm('');
                                        router.get('/admin/potentials', {}, { replace: true });
                                    }}
                                    className="w-full"
                                >
                                    Reset Filter
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Potentials Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <MapPin className="h-5 w-5" />
                                <span>Daftar Potensi ({potentials.total})</span>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {potentials.data.length > 0 ? (
                            <div className="space-y-4">
                                <div className="overflow-x-auto">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Nama Potensi</TableHead>
                                                <TableHead>Tipe</TableHead>
                                                <TableHead>Lokasi</TableHead>
                                                <TableHead>Gambar</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Tanggal Dibuat</TableHead>
                                                <TableHead className="text-right">Aksi</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {potentials.data.map((potential) => (
                                                <TableRow key={potential.id}>
                                                    <TableCell>
                                                        <div className="space-y-1">
                                                            <div className="font-medium text-gray-900 dark:text-gray-100">{potential.name}</div>
                                                            <div className="line-clamp-2 text-sm text-gray-500 dark:text-gray-400">
                                                                {potential.description}
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge className={getTypeColor(potential.type)}>{getTypeLabel(potential.type)}</Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <span className="text-sm">{potential.location || '-'}</span>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="flex items-center space-x-2">
                                                            <span className="text-sm">{potential.images.length} foto</span>
                                                            {potential.images.length > 0 && (
                                                                <div className="h-8 w-8 overflow-hidden rounded">
                                                                    <img
                                                                        src={`/storage/${potential.images[0]}`}
                                                                        alt={potential.name}
                                                                        className="h-full w-full object-cover"
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="flex items-center space-x-2">
                                                            {potential.is_featured ? (
                                                                <>
                                                                    <Star className="h-4 w-4 fill-current text-yellow-500" />
                                                                    <span className="text-sm text-yellow-600">Unggulan</span>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <StarOff className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                                                                    <span className="text-sm text-gray-500 dark:text-gray-400">Biasa</span>
                                                                </>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="text-sm">{new Date(potential.created_at).toLocaleDateString('id-ID')}</div>
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex items-center justify-end space-x-2">
                                                            <Link href={route('admin.potentials.show', potential.id)}>
                                                                <Button variant="ghost" size="sm">
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Link href={route('admin.potentials.edit', potential.id)}>
                                                                <Button variant="ghost" size="sm">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button variant="ghost" size="sm" onClick={() => handleToggleFeatured(potential)}>
                                                                {potential.is_featured ? (
                                                                    <StarOff className="h-4 w-4" />
                                                                ) : (
                                                                    <Star className="h-4 w-4" />
                                                                )}
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => handleDelete(potential)}
                                                                className="text-red-600 hover:text-red-800"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Pagination */}
                                {potentials.last_page > 1 && (
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            Menampilkan {potentials.from} - {potentials.to} dari {potentials.total} potensi
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {potentials.links.map((link, index) => (
                                                <Link
                                                    key={index}
                                                    href={link.url || '#'}
                                                    preserveState
                                                    preserveScroll
                                                    className={`rounded-md border px-3 py-1 text-sm ${
                                                        link.active
                                                            ? 'border-blue-600 bg-blue-600 text-white'
                                                            : link.url
                                                              ? 'hover:bg-gray-100 dark:hover:bg-gray-700'
                                                              : 'cursor-not-allowed opacity-50'
                                                    }`}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="py-12 text-center">
                                <MapPin className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                                <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">Belum ada potensi</h3>
                                <p className="mt-2 text-gray-500 dark:text-gray-400">
                                    {Object.values(filters).some(Boolean)
                                        ? 'Tidak ada potensi yang sesuai dengan filter yang dipilih.'
                                        : 'Mulai dengan menambahkan potensi wisata atau UMKM desa.'}
                                </p>
                                <div className="mt-6">
                                    <Link href={route('admin.potentials.create')}>
                                        <Button>
                                            <Plus className="mr-2 h-4 w-4" />
                                            Tambah Potensi
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
