<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - Kesalahan Server | {{ config('app.name') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f9fafb;
            color: #374151;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .container {
            max-width: 28rem;
            width: 100%;
            text-align: center;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .icon {
            width: 6rem;
            height: 6rem;
            margin: 0 auto 1rem;
            color: #ef4444;
        }
        
        .error-code {
            font-size: 2.5rem;
            font-weight: bold;
            color: #111827;
            margin-bottom: 0.5rem;
        }
        
        .error-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
        }
        
        .error-message {
            color: #6b7280;
            margin-bottom: 2rem;
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
        }
        
        .btn-secondary {
            background-color: white;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn-secondary:hover {
            background-color: #f9fafb;
        }
        
        .contact-info {
            margin-top: 2rem;
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .contact-email {
            font-weight: 500;
            color: #374151;
        }
        
        @media (min-width: 640px) {
            .buttons {
                flex-direction: row;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
        </svg>
        
        <h1 class="error-code">500</h1>
        <h2 class="error-title">Kesalahan Server</h2>
        <p class="error-message">
            Maaf, terjadi kesalahan pada server kami. Tim teknis telah diberitahu dan sedang memperbaiki masalah ini. 
            Silakan coba lagi dalam beberapa saat.
        </p>
        
        <div class="buttons">
            <button onclick="location.reload()" class="btn btn-secondary">Muat Ulang</button>
            <button onclick="history.back()" class="btn btn-secondary">Kembali</button>
            <a href="{{ route('home') }}" class="btn btn-primary">Ke Beranda</a>
        </div>
        
        <div class="contact-info">
            <p>Jika masalah berlanjut, silakan hubungi administrator desa di:</p>
            <p class="contact-email"><EMAIL></p>
        </div>
    </div>
</body>
</html>