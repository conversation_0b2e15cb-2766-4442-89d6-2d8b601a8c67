<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminUsers = [
            [
                'name' => 'Admin Desa Lemah Duhur',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Suyanto',
                'email' => '<EMAIL>',
                'password' => Hash::make('kepala123'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Dwi Hartono',
                'email' => '<EMAIL>',
                'password' => Hash::make('sekdes123'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ],
        ];

        foreach ($adminUsers as $admin) {
            User::create($admin);
        }
    }
}
