<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\Setting;
use App\Services\SEOService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ServiceController extends Controller
{
    public function __construct(
        private SEOService $seoService
    ) {}

    /**
     * Display a listing of active services for public view
     */
    public function index(Request $request)
    {
        $search = $request->get('search');

        $servicesQuery = Service::active()->orderBy('name');

        if ($search) {
            $servicesQuery->where('name', 'like', '%'.$search.'%');
        }

        // Device detection
        $userAgent = $request->header('User-Agent', '');
        $isMobile = $this->isMobileDevice($userAgent);
        $isTablet = $this->isTabletDevice($userAgent);

        $services = $servicesQuery->get()->map(function ($service) use ($isMobile) {
            $serviceData = [
                'id' => $service->id,
                'name' => $service->name,
                'description' => $service->description,
                'cost' => $service->cost,
                'formatted_cost' => $service->formatted_cost,
                'processing_time' => $service->processing_time,
                'requirements_list' => $service->requirements_list,
                'procedure_list' => $service->procedure_list,
            ];

            // Add condensed property for mobile devices
            if ($isMobile) {
                $serviceData['condensed'] = true;
            }

            return $serviceData;
        });

        // Generate SEO meta tags
        $seoMeta = $this->seoService->generateMetaTags('services.index');
        $structuredData = $this->seoService->generateStructuredData('services.index');

        // Combine SEO data as expected by tests
        $seo = array_merge($seoMeta, [
            'og_title' => $seoMeta['title'],
            'og_description' => $seoMeta['description'],
            'og_type' => $seoMeta['type'],
            'og_url' => $seoMeta['url'],
            'og_image' => $seoMeta['image'] ?? null,
            'twitter_card' => 'summary_large_image',
            'twitter_title' => $seoMeta['title'],
            'twitter_description' => $seoMeta['description'],
            'twitter_image' => $seoMeta['image'] ?? null,
            'canonical' => $seoMeta['url'],
            'structured_data' => $structuredData,
        ]);

        // Batch get settings to reduce queries
        $settingsData = cache()->remember('services_page_settings', 1800, function () {
            return Setting::whereIn('key', [
                'village.operating_hours',
                'village.contact_info',
                'village.profile',
                'village.emergency_contacts',
                'village.history',
                'village.service_settings',
            ])->pluck('value', 'key')->toArray();
        });

        $operatingHoursData = $settingsData['village.operating_hours'] ?? '{}';
        $operatingHours = is_array($operatingHoursData)
            ? $operatingHoursData
            : json_decode($operatingHoursData, true);
        $operatingHours = $operatingHours ?: [
            'weekdays' => 'Senin - Jumat: 08:00 - 15:00 WIB',
            'saturday' => 'Sabtu: 08:00 - 12:00 WIB',
            'sunday' => 'Minggu: Tutup',
            'break' => 'Istirahat: 12:00 - 13:00 WIB',
        ];

        $contactInfoData = $settingsData['village.contact_info'] ?? '{}';
        $contactInfo = is_array($contactInfoData)
            ? $contactInfoData
            : json_decode($contactInfoData, true);
        $contactInfo = $contactInfo ?: [
            'phone' => '(0251) 8240xxx',
            'email' => '<EMAIL>',
            'address' => 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat',
        ];

        // Get all settings for frontend access
        $settings = [
            'village.contact_info' => $contactInfo,
            'village.profile' => (isset($settingsData['village.profile'])
                ? (is_array($settingsData['village.profile'])
                    ? $settingsData['village.profile']
                    : json_decode($settingsData['village.profile'], true))
                : []) ?: [
                    'name' => 'Desa Lemah Duhur',
                    'district' => 'Kecamatan Caringin',
                    'regency' => 'Kabupaten Bogor',
                    'province' => 'Jawa Barat',
                ],
            'village.operating_hours' => $operatingHours,
            'village.emergency_contacts' => isset($settingsData['village.emergency_contacts'])
                ? (is_array($settingsData['village.emergency_contacts'])
                    ? $settingsData['village.emergency_contacts']
                    : json_decode($settingsData['village.emergency_contacts'], true))
                : [],
            'village.history' => isset($settingsData['village.history'])
                ? (is_array($settingsData['village.history'])
                    ? $settingsData['village.history']
                    : json_decode($settingsData['village.history'], true))
                : [],
            'village.service_settings' => isset($settingsData['village.service_settings'])
                ? (is_array($settingsData['village.service_settings'])
                    ? $settingsData['village.service_settings']
                    : json_decode($settingsData['village.service_settings'], true))
                : [],
        ];

        return Inertia::render('Public/Services/Index', [
            'services' => $services,
            'operatingHours' => $operatingHours,
            'contactInfo' => $contactInfo,
            'settings' => $settings,
            'seo' => $seo,
            'touchOptimized' => $isMobile,
            'formOptimization' => [
                'touchFriendly' => $isMobile,
            ],
            'accessibility' => [
                'keyboardNavigation' => true,
                'mobileNavigation' => [
                    'ariaLabels' => true,
                ],
            ],
        ]);
    }

    private function isMobileDevice(string $userAgent): bool
    {
        return preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $userAgent) &&
               ! preg_match('/iPad/i', $userAgent);
    }

    private function isTabletDevice(string $userAgent): bool
    {
        return preg_match('/iPad|Android.*Tablet|Kindle|Silk/i', $userAgent);
    }

    /**
     * Display the specified service details
     */
    public function show($serviceId)
    {
        $service = Service::find($serviceId);

        // Return 404 if service doesn't exist or is not active
        abort_if(! $service || ! $service->is_active, 404);

        // Generate SEO meta tags
        $seoMeta = $this->seoService->generateMetaTags('services.show', ['service' => $service]);
        $structuredData = $this->seoService->generateStructuredData('services.show', ['service' => $service]);

        // Combine SEO data as expected by tests
        $seo = array_merge($seoMeta, [
            'og_title' => $seoMeta['title'],
            'og_description' => $seoMeta['description'],
            'og_type' => $seoMeta['type'],
            'og_url' => $seoMeta['url'],
            'og_image' => $seoMeta['image'] ?? null,
            'twitter_card' => 'summary_large_image',
            'twitter_title' => $seoMeta['title'],
            'twitter_description' => $seoMeta['description'],
            'twitter_image' => $seoMeta['image'] ?? null,
            'canonical' => $seoMeta['url'],
            'structured_data' => $structuredData,
        ]);

        // Get contact information from database with fallback values
        $contactInfo = Setting::get('village.contact_info', [
            'phone' => '(0251) 8240xxx',
            'email' => '<EMAIL>',
            'address' => 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat',
        ]);

        // Get all settings for frontend access
        $settings = [
            'village.contact_info' => $contactInfo,
            'village.profile' => Setting::get('village.profile', [
                'name' => 'Desa Lemah Duhur',
                'district' => 'Kecamatan Caringin',
                'regency' => 'Kabupaten Bogor',
                'province' => 'Jawa Barat',
            ]),
            'village.operating_hours' => Setting::get('village.operating_hours', []),
            'village.emergency_contacts' => Setting::get('village.emergency_contacts', []),
            'village.history' => Setting::get('village.history', []),
            'village.service_settings' => Setting::get('village.service_settings', []),
        ];

        return Inertia::render('Public/Services/Detail', [
            'service' => [
                'id' => $service->id,
                'name' => $service->name,
                'description' => $service->description,
                'requirements' => $service->requirements_list,
                'procedure' => $service->procedure_list,
                'cost' => $service->formatted_cost,
                'processing_time' => $service->processing_time,
                'contact_info' => $service->contact_info,
            ],
            'contactInfo' => $contactInfo,
            'settings' => $settings,
            'seoMeta' => $seoMeta,
            'structuredData' => $structuredData,
        ]);
    }
}
