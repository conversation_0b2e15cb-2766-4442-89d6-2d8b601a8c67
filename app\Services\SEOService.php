<?php

namespace App\Services;

use App\Models\News;
use App\Models\Potential;
use App\Models\Service;
use Illuminate\Support\Str;

class SEOService
{
    private const DEFAULT_TITLE = 'Desa Lemah Duhur - Kecamatan Caringin, Kabupaten Bogor';

    private const DEFAULT_DESCRIPTION = 'Website resmi Desa Lemah Duhur, Kecamatan Caringin, Kabupaten Bogor, Jawa Barat. Informasi profil desa, layanan publik, berita terkini, dan potensi wisata serta UMKM.';

    private const DEFAULT_IMAGE = '/images/logo-desa.png';

    private const SITE_URL = 'https://lemah-duhur.desa.id';

    private function getSiteUrl(): string
    {
        // Use app URL for testing environment
        if (app()->environment('testing')) {
            return config('app.url');
        }

        return self::SITE_URL;
    }

    public function generateMetaTags(string $page, array $data = []): array
    {
        $siteUrl = $this->getSiteUrl();

        $meta = [
            'title' => self::DEFAULT_TITLE,
            'description' => self::DEFAULT_DESCRIPTION,
            'image' => self::DEFAULT_IMAGE,
            'url' => $siteUrl,
            'type' => 'website',
            'site_name' => 'Desa Lemah Duhur',
            'locale' => 'id_ID',
        ];

        switch ($page) {
            case 'home':
                $meta = $this->getHomeMeta($meta);
                break;
            case 'profile':
                $meta = $this->getProfileMeta($meta);
                break;
            case 'news.index':
                $meta = $this->getNewsIndexMeta($meta);
                break;
            case 'news.show':
                $meta = $this->getNewsShowMeta($meta, $data);
                break;
            case 'services.index':
                $meta = $this->getServicesIndexMeta($meta);
                break;
            case 'services.show':
                $meta = $this->getServicesShowMeta($meta, $data);
                break;
            case 'potential.index':
                $meta = $this->getPotentialIndexMeta($meta);
                break;
            case 'potential.show':
                $meta = $this->getPotentialShowMeta($meta, $data);
                break;
        }

        return $meta;
    }

    private function getHomeMeta(array $meta): array
    {
        $siteUrl = $this->getSiteUrl();

        $meta['title'] = 'Beranda - Desa Lemah Duhur, Caringin, Bogor';
        $meta['description'] = 'Selamat datang di website resmi Desa Lemah Duhur. Temukan informasi profil desa, layanan publik, berita, dan potensi wisata serta UMKM.';
        $meta['url'] = $siteUrl;

        return $meta;
    }

    private function getProfileMeta(array $meta): array
    {
        $siteUrl = $this->getSiteUrl();

        $meta['title'] = 'Profil Desa - '.self::DEFAULT_TITLE;
        $meta['description'] = 'profil lengkap desa Lemah Duhur meliputi sejarah, visi misi, struktur organisasi, data demografis, dan informasi geografis desa di Kecamatan Caringin, Kabupaten Bogor.';
        $meta['url'] = $siteUrl.'/profil';

        return $meta;
    }

    private function getNewsIndexMeta(array $meta): array
    {
        $siteUrl = $this->getSiteUrl();

        $meta['title'] = 'Berita Desa - '.self::DEFAULT_TITLE;
        $meta['description'] = 'Berita terkini dan pengumuman resmi dari Desa Lemah Duhur. Ikuti perkembangan kegiatan, pembangunan, dan informasi penting lainnya dari desa kami.';
        $meta['url'] = $siteUrl.'/berita';

        return $meta;
    }

    private function getNewsShowMeta(array $meta, array $data): array
    {
        $siteUrl = $this->getSiteUrl();

        if (isset($data['news']) && $data['news'] instanceof News) {
            $news = $data['news'];

            $meta['title'] = $news->meta_title ?: ($news->title.' - Berita Desa Lemah Duhur');
            $meta['description'] = $news->meta_description ?: Str::limit(strip_tags($news->excerpt ?: $news->content), 160);
            $featuredImageUrl = $news->getFeaturedImageUrl('medium');
            $meta['image'] = $featuredImageUrl ? asset($featuredImageUrl) : self::DEFAULT_IMAGE;
            $meta['url'] = $siteUrl.'/berita/'.$news->slug;
            $meta['type'] = 'article';
            $meta['published_time'] = $news->published_at?->toISOString();
            $meta['modified_time'] = $news->updated_at->toISOString();
        }

        return $meta;
    }

    private function getServicesIndexMeta(array $meta): array
    {
        $siteUrl = $this->getSiteUrl();

        $meta['title'] = 'Layanan Publik - '.self::DEFAULT_TITLE;
        $meta['description'] = 'Layanan publik Desa Lemah Duhur meliputi pengurusan surat keterangan, administrasi kependudukan, dan berbagai layanan pemerintahan desa lainnya.';
        $meta['url'] = $siteUrl.'/layanan';

        return $meta;
    }

    private function getServicesShowMeta(array $meta, array $data): array
    {
        $siteUrl = $this->getSiteUrl();

        if (isset($data['service']) && $data['service'] instanceof Service) {
            $service = $data['service'];

            $meta['title'] = $service->name.' - Layanan Desa Lemah Duhur';
            $meta['description'] = Str::limit(strip_tags($service->description), 160);
            $meta['url'] = $siteUrl.'/layanan/'.Str::slug($service->name);
        }

        return $meta;
    }

    private function getPotentialIndexMeta(array $meta): array
    {
        $siteUrl = $this->getSiteUrl();

        $meta['title'] = 'Potensi Desa - '.self::DEFAULT_TITLE;
        $meta['description'] = 'potensi wisata dan UMKM Desa Lemah Duhur. Temukan destinasi wisata menarik dan produk unggulan dari usaha mikro kecil menengah di desa kami.';
        $meta['url'] = $siteUrl.'/potensi';

        return $meta;
    }

    private function getPotentialShowMeta(array $meta, array $data): array
    {
        $siteUrl = $this->getSiteUrl();

        if (isset($data['potential']) && $data['potential'] instanceof Potential) {
            $potential = $data['potential'];

            $meta['title'] = $potential->name.' - Potensi Desa Lemah Duhur';
            $meta['description'] = Str::limit(strip_tags($potential->description), 160);
            $meta['image'] = empty($potential->images) ? self::DEFAULT_IMAGE : asset('storage/'.$potential->images[0]);
            $meta['url'] = $siteUrl.'/potensi/'.Str::slug($potential->name);
        }

        return $meta;
    }

    public function generateStructuredData(string $page, array $data = []): array
    {
        $siteUrl = $this->getSiteUrl();

        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'Desa Lemah Duhur',
            'url' => $siteUrl,
            'logo' => asset(self::DEFAULT_IMAGE),
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => 'Desa Lemah Duhur',
                'addressLocality' => 'Caringin',
                'addressRegion' => 'Bogor',
                'addressCountry' => 'ID',
            ],
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'contactType' => 'customer service',
                'availableLanguage' => 'Indonesian',
            ],
        ];

        // Override structured data based on page type
        switch ($page) {
            case 'profile':
                $structuredData['@type'] = 'Organization';
                break;
            case 'services.index':
                $structuredData['@type'] = 'GovernmentService';
                break;
            case 'potential.index':
                $structuredData['@type'] = 'TouristAttraction';
                break;
            case 'news.show':
                if (isset($data['news'])) {
                    $news = $data['news'];
                    $structuredData = [
                        '@context' => 'https://schema.org',
                        '@type' => 'NewsArticle',
                        'headline' => $news->title,
                        'description' => Str::limit(strip_tags($news->excerpt ?: $news->content), 160),
                        'image' => $news->featured_image ? asset('storage/'.$news->featured_image) : asset(self::DEFAULT_IMAGE),
                        'datePublished' => $news->published_at?->toISOString(),
                        'dateModified' => $news->updated_at->toISOString(),
                        'author' => [
                            '@type' => 'Organization',
                            'name' => 'Pemerintah Desa Lemah Duhur',
                        ],
                        'publisher' => [
                            '@type' => 'Organization',
                            'name' => 'Desa Lemah Duhur',
                            'logo' => [
                                '@type' => 'ImageObject',
                                'url' => asset(self::DEFAULT_IMAGE),
                            ],
                        ],
                    ];
                }
                break;
        }

        return $structuredData;
    }

    public function generateSitemapData(): array
    {
        $urls = [];
        $siteUrl = $this->getSiteUrl();

        // Static pages
        $staticPages = [
            ['url' => '/', 'priority' => '1.0', 'changefreq' => 'daily'],
            ['url' => '/profil', 'priority' => '0.9', 'changefreq' => 'monthly'],
            ['url' => '/layanan', 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['url' => '/berita', 'priority' => '0.8', 'changefreq' => 'daily'],
            ['url' => '/potensi', 'priority' => '0.7', 'changefreq' => 'monthly'],
        ];

        foreach ($staticPages as $page) {
            // Handle homepage URL properly (avoid double slash)
            $url = $page['url'] === '/' ? $siteUrl : $siteUrl.$page['url'];

            $urls[] = [
                'loc' => $url,
                'lastmod' => now()->toISOString(),
                'changefreq' => $page['changefreq'],
                'priority' => $page['priority'],
            ];
        }

        // News pages
        $news = News::where('is_published', true)
            ->orderBy('published_at', 'desc')
            ->get();

        foreach ($news as $article) {
            $urls[] = [
                'loc' => $siteUrl.'/berita/'.$article->slug,
                'lastmod' => $article->updated_at->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.6',
            ];
        }

        // Services pages
        $services = Service::where('is_active', true)->get();
        foreach ($services as $service) {
            $urls[] = [
                'loc' => $siteUrl.'/layanan/'.Str::slug($service->name),
                'lastmod' => $service->updated_at->toISOString(),
                'changefreq' => 'monthly',
                'priority' => '0.5',
            ];
        }

        // Potential pages
        $potentials = Potential::all();
        foreach ($potentials as $potential) {
            $urls[] = [
                'loc' => $siteUrl.'/potensi/'.Str::slug($potential->name),
                'lastmod' => $potential->updated_at->toISOString(),
                'changefreq' => 'monthly',
                'priority' => '0.5',
            ];
        }

        return $urls;
    }
}
