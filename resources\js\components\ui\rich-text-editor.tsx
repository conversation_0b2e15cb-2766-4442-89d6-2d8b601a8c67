import { cn } from '@/lib/utils';
import React, { useEffect, useRef, useState } from 'react';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import '../../../css/rich-text-editor.css';

interface RichTextEditorProps {
    value?: string;
    onChange?: (value: string) => void;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
    error?: boolean;
}

const RichTextEditor = React.forwardRef<HTMLDivElement, RichTextEditorProps>(
    ({ value = '', onChange, placeholder = 'Masukkan konten...', className, disabled = false, error = false }, ref) => {
        const editorRef = useRef<HTMLDivElement>(null);
        const quillRef = useRef<Quill | null>(null);
        const [isReady, setIsReady] = useState(false);
        const [lastValue, setLastValue] = useState<string>('');

        useEffect(() => {
            if (!editorRef.current || quillRef.current) return;

            // Quill configuration optimized for village profile content
            const quill = new Quill(editorRef.current, {
                theme: 'snow',
                placeholder,
                modules: {
                    toolbar: [
                        // Text formatting
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        ['bold', 'italic', 'underline'],

                        // Lists and alignment
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'align': [] }],

                        // Structure
                        ['blockquote'],

                        // Cleanup
                        ['clean']
                    ],
                    clipboard: {
                        // Allow more HTML when pasting to preserve existing content
                        matchVisual: true,
                    }
                },
                formats: [
                    'header', 'bold', 'italic', 'underline',
                    'list', 'bullet', 'align',
                    'blockquote'
                ]
            });

            quillRef.current = quill;

            // Set initial content if provided
            if (value) {
                quill.root.innerHTML = value;
                setLastValue(value);
            }

            // Handle content changes
            quill.on('text-change', () => {
                const html = quill.root.innerHTML;
                const cleanHtml = html === '<p><br></p>' ? '' : html;
                // Only call onChange if content actually changed
                if (cleanHtml !== lastValue && onChange) {
                    setLastValue(cleanHtml);
                    onChange(cleanHtml);
                }
            });

            // Handle disabled state
            quill.enable(!disabled);

            setIsReady(true);

            return () => {
                if (quillRef.current) {
                    quillRef.current = null;
                }
            };
        }, []);

        // Update content when value prop changes
        useEffect(() => {
            if (quillRef.current && isReady && value !== lastValue) {
                const quill = quillRef.current;
                const currentSelection = quill.getSelection();

                // Temporarily disable text-change listener to prevent infinite loop
                quill.off('text-change');

                quill.root.innerHTML = value || '';
                setLastValue(value || '');

                // Re-enable text-change listener
                quill.on('text-change', () => {
                    const html = quill.root.innerHTML;
                    const cleanHtml = html === '<p><br></p>' ? '' : html;
                    if (cleanHtml !== lastValue && onChange) {
                        setLastValue(cleanHtml);
                        onChange(cleanHtml);
                    }
                });

                // Restore selection if it was valid
                if (currentSelection) {
                    quill.setSelection(currentSelection);
                }
            }
        }, [value, isReady, lastValue, onChange]);

        // Update disabled state
        useEffect(() => {
            if (quillRef.current && isReady) {
                quillRef.current.enable(!disabled);
            }
        }, [disabled, isReady]);

        return (
            <div 
                className={cn(
                    'rich-text-editor',
                    error && 'rich-text-editor--error',
                    disabled && 'rich-text-editor--disabled',
                    className
                )}
                ref={ref}
            >
                <div
                    ref={editorRef}
                    className={cn(
                        'min-h-[200px]',
                        error && 'border-red-500'
                    )}
                />
            </div>
        );
    }
);

RichTextEditor.displayName = 'RichTextEditor';

export { RichTextEditor };
