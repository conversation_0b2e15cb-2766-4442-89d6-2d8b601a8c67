<?php

namespace Tests\Feature;

use App\Jobs\SendComplaintNotification;
use App\Jobs\SendComplaintStatusUpdate;
use App\Models\Complaint;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class ComplaintTrackingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Queue::fake();
    }

    public function test_can_access_complaint_tracking_page(): void
    {
        $response = $this->get(route('complaints.tracking'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/ComplaintTracking')
            ->has('pageTitle')
            ->has('pageDescription')
        );
    }

    public function test_can_track_complaint_with_valid_ticket(): void
    {
        $complaint = Complaint::factory()->create([
            'ticket_number' => 'LDH-20240724-001',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'status' => 'pending',
        ]);

        $response = $this->post(route('complaints.track'), [
            'ticket_number' => 'LDH-20240724-001',
        ]);

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/ComplaintDetail')
            ->has('complaint')
            ->where('complaint.ticket_number', 'LDH-20240724-001')
            ->where('complaint.name', 'John Doe')
            ->where('complaint.status', 'pending')
        );
    }

    public function test_cannot_track_complaint_with_invalid_ticket(): void
    {
        $response = $this->post(route('complaints.track'), [
            'ticket_number' => 'INVALID-TICKET',
        ]);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['ticket_number']);
    }

    public function test_complaint_creation_dispatches_notification_job(): void
    {
        $complaintData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '081234567890',
            'category' => 'infrastruktur',
            'subject' => 'Jalan Rusak',
            'description' => 'Jalan di depan rumah saya rusak parah dan perlu diperbaiki segera. Kondisi ini sudah berlangsung selama 2 minggu.',
        ];

        $response = $this->post(route('complaints.store'), $complaintData);

        $response->assertStatus(302);

        // Check that notification job was dispatched
        Queue::assertPushed(SendComplaintNotification::class, function ($job) {
            return $job->complaint->name === 'John Doe';
        });
    }

    public function test_complaint_status_update_dispatches_notification_job(): void
    {
        $user = User::factory()->create(['role' => 'admin']);

        $complaint = Complaint::factory()->create([
            'status' => 'pending',
            'email' => '<EMAIL>',
        ]);

        // Update status
        $complaint->update([
            'status' => 'in_progress',
            'admin_response' => 'Pengaduan sedang kami proses',
            'responded_by' => $user->id,
        ]);

        // Check that status update job was dispatched
        Queue::assertPushed(SendComplaintStatusUpdate::class, function ($job) use ($complaint) {
            return $job->complaint->id === $complaint->id &&
                   $job->previousStatus === 'pending';
        });
    }

    public function test_complaint_detail_page_shows_correct_information(): void
    {
        $user = User::factory()->create([
            'name' => 'Admin User',
            'role' => 'admin',
        ]);

        $complaint = Complaint::factory()->create([
            'ticket_number' => 'LDH-20240724-001',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '081234567890',
            'category' => 'infrastruktur',
            'subject' => 'Jalan Rusak',
            'description' => 'Jalan rusak parah',
            'status' => 'resolved',
            'admin_response' => 'Sudah diperbaiki',
            'responded_by' => $user->id,
            'responded_at' => now(),
        ]);

        $response = $this->post(route('complaints.track'), [
            'ticket_number' => 'LDH-20240724-001',
        ]);

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/ComplaintDetail')
            ->has('complaint')
            ->where('complaint.ticket_number', 'LDH-20240724-001')
            ->where('complaint.name', 'John Doe')
            ->where('complaint.status', 'resolved')
            ->where('complaint.admin_response', 'Sudah diperbaiki')
            ->has('complaint.responded_by')
            ->where('complaint.responded_by.name', 'Admin User')
        );
    }

    public function test_complaint_tracking_requires_ticket_number(): void
    {
        $response = $this->post(route('complaints.track'), []);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['ticket_number']);
    }

    public function test_complaint_tracking_validates_ticket_format(): void
    {
        $response = $this->post(route('complaints.track'), [
            'ticket_number' => '',
        ]);

        $response->assertStatus(302);
        $response->assertSessionHasErrors(['ticket_number']);
    }
}
