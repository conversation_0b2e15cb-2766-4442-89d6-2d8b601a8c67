import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, ImageIcon, MapPin, Save, X } from 'lucide-react';
import { useRef, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Potensi',
        href: '/admin/potentials',
    },
    {
        title: '<PERSON>bah Potensi',
        href: '/admin/potentials/create',
    },
];

export default function CreatePotential() {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        type: '' as 'tourism' | 'umkm' | '',
        description: '',
        location: '',
        contact_info: {
            phone: '',
            email: '',
            person: '',
            address: '',
            website: '',
        },
        images: [] as File[],
        is_featured: false as boolean,
    });

    const [imagePreviews, setImagePreviews] = useState<string[]>([]);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.potentials.store'));
    };

    const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        if (files.length === 0) return;

        // Limit to 10 images total
        const currentImages = data.images.length;
        const availableSlots = 10 - currentImages;
        const filesToAdd = files.slice(0, availableSlots);

        // Create previews
        const newPreviews: string[] = [];
        filesToAdd.forEach((file) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                newPreviews.push(e.target?.result as string);
                if (newPreviews.length === filesToAdd.length) {
                    setImagePreviews([...imagePreviews, ...newPreviews]);
                }
            };
            reader.readAsDataURL(file);
        });

        setData('images', [...data.images, ...filesToAdd]);
    };

    const removeImage = (index: number) => {
        const newImages = data.images.filter((_, i) => i !== index);
        const newPreviews = imagePreviews.filter((_, i) => i !== index);
        setData('images', newImages);
        setImagePreviews(newPreviews);
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Tambah Potensi - Admin Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Tambah Potensi</h1>
                        <p className="text-gray-600 dark:text-gray-300">Buat potensi wisata atau UMKM baru untuk desa</p>
                    </div>
                    <Link href={route('admin.potentials.index')}>
                        <Button variant="outline" className="flex items-center space-x-2">
                            <ArrowLeft className="h-4 w-4" />
                            <span>Kembali</span>
                        </Button>
                    </Link>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <MapPin className="h-5 w-5" />
                                <span>Informasi Dasar</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Nama Potensi *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="Contoh: Air Terjun Curug Nangka"
                                        className={errors.name ? 'border-red-500' : ''}
                                    />
                                    {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="type">Tipe Potensi *</Label>
                                    <Select value={data.type} onValueChange={(value: 'tourism' | 'umkm') => setData('type', value)}>
                                        <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Pilih tipe potensi" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="tourism">Wisata</SelectItem>
                                            <SelectItem value="umkm">UMKM</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.type && <p className="text-sm text-red-600">{errors.type}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="location">Lokasi</Label>
                                <Input
                                    id="location"
                                    value={data.location}
                                    onChange={(e) => setData('location', e.target.value)}
                                    placeholder="Contoh: Kampung Cimande, RT 02/RW 05"
                                    className={errors.location ? 'border-red-500' : ''}
                                />
                                {errors.location && <p className="text-sm text-red-600">{errors.location}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Deskripsi Potensi *</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder="Jelaskan potensi ini secara detail..."
                                    rows={4}
                                    className={errors.description ? 'border-red-500' : ''}
                                />
                                {errors.description && <p className="text-sm text-red-600">{errors.description}</p>}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Images */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <ImageIcon className="h-5 w-5" />
                                <span>Gambar Potensi</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label>Upload Gambar (Maksimal 10 foto)</Label>
                                <div className="flex items-center space-x-4">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => fileInputRef.current?.click()}
                                        disabled={data.images.length >= 10}
                                        className="flex items-center space-x-2"
                                    >
                                        <ImageIcon className="h-4 w-4" />
                                        <span>Pilih Gambar</span>
                                    </Button>
                                    <span className="text-sm text-gray-500 dark:text-gray-400">{data.images.length}/10 gambar dipilih</span>
                                </div>
                                <input ref={fileInputRef} type="file" multiple accept="image/*" onChange={handleImageUpload} className="hidden" />
                                {errors.images && <p className="text-sm text-red-600">{errors.images}</p>}
                            </div>

                            {/* Image Previews */}
                            {imagePreviews.length > 0 && (
                                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                                    {imagePreviews.map((preview, index) => (
                                        <div key={index} className="relative">
                                            <img src={preview} alt={`Preview ${index + 1}`} className="h-24 w-full rounded-lg object-cover" />
                                            <Button
                                                type="button"
                                                variant="destructive"
                                                size="sm"
                                                onClick={() => removeImage(index)}
                                                className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                            >
                                                <X className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Contact Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Informasi Kontak</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="contact_phone">Nomor Telepon</Label>
                                    <Input
                                        id="contact_phone"
                                        value={data.contact_info.phone}
                                        onChange={(e) => setData('contact_info', { ...data.contact_info, phone: e.target.value })}
                                        placeholder="Contoh: 0812-3456-7890"
                                        className={errors['contact_info.phone'] ? 'border-red-500' : ''}
                                    />
                                    {errors['contact_info.phone'] && <p className="text-sm text-red-600">{errors['contact_info.phone']}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="contact_email">Email</Label>
                                    <Input
                                        id="contact_email"
                                        type="email"
                                        value={data.contact_info.email}
                                        onChange={(e) => setData('contact_info', { ...data.contact_info, email: e.target.value })}
                                        placeholder="Contoh: <EMAIL>"
                                        className={errors['contact_info.email'] ? 'border-red-500' : ''}
                                    />
                                    {errors['contact_info.email'] && <p className="text-sm text-red-600">{errors['contact_info.email']}</p>}
                                </div>
                            </div>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="contact_person">Penanggung Jawab</Label>
                                    <Input
                                        id="contact_person"
                                        value={data.contact_info.person}
                                        onChange={(e) => setData('contact_info', { ...data.contact_info, person: e.target.value })}
                                        placeholder="Contoh: Bapak Suharto"
                                        className={errors['contact_info.person'] ? 'border-red-500' : ''}
                                    />
                                    {errors['contact_info.person'] && <p className="text-sm text-red-600">{errors['contact_info.person']}</p>}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="contact_website">Website</Label>
                                    <Input
                                        id="contact_website"
                                        type="url"
                                        value={data.contact_info.website}
                                        onChange={(e) => setData('contact_info', { ...data.contact_info, website: e.target.value })}
                                        placeholder="Contoh: https://wisatalemahduhur.com"
                                        className={errors['contact_info.website'] ? 'border-red-500' : ''}
                                    />
                                    {errors['contact_info.website'] && <p className="text-sm text-red-600">{errors['contact_info.website']}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="contact_address">Alamat Lengkap</Label>
                                <Textarea
                                    id="contact_address"
                                    value={data.contact_info.address}
                                    onChange={(e) => setData('contact_info', { ...data.contact_info, address: e.target.value })}
                                    placeholder="Contoh: Jl. Raya Cimande No. 123, Kampung Cimande, Desa Lemah Duhur"
                                    rows={2}
                                    className={errors['contact_info.address'] ? 'border-red-500' : ''}
                                />
                                {errors['contact_info.address'] && <p className="text-sm text-red-600">{errors['contact_info.address']}</p>}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Status */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Status Potensi</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="is_featured"
                                    checked={data.is_featured}
                                    onCheckedChange={(checked: boolean) => setData('is_featured', checked)}
                                />
                                <Label htmlFor="is_featured">Jadikan potensi unggulan</Label>
                            </div>
                            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Potensi unggulan akan ditampilkan di halaman utama</p>
                        </CardContent>
                    </Card>

                    {/* Submit Button */}
                    <div className="flex justify-end space-x-4">
                        <Link href={route('admin.potentials.index')}>
                            <Button type="button" variant="outline">
                                Batal
                            </Button>
                        </Link>
                        <Button type="submit" disabled={processing} className="flex items-center space-x-2">
                            <Save className="h-4 w-4" />
                            <span>{processing ? 'Menyimpan...' : 'Simpan Potensi'}</span>
                        </Button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
