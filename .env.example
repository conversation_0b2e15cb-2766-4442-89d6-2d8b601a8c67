APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

# Test Database Configuration
DB_TEST_CONNECTION=sqlite
DB_TEST_DATABASE=:memory:

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Complaint System Email Configuration
MAIL_ADMIN_EMAIL="<EMAIL>"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Performance Configuration
# PAGE_CACHE_ENABLED=true
# PAGE_CACHE_TTL=3600

# ASSET_COMPRESSION_ENABLED=true
# ASSET_COMPRESSION_LEVEL=9
# ASSET_MINIFICATION_ENABLED=true
# ASSET_VERSIONING_ENABLED=true

# DB_QUERY_CACHE_ENABLED=true
# DB_QUERY_CACHE_TTL=3600
# DB_ANALYZE_TABLES=true
# DB_CLEANUP_CACHE=true

# IMAGE_OPTIMIZATION_ENABLED=true
# IMAGE_QUALITY=85
# WEBP_ENABLED=true
# WEBP_QUALITY=80
# LAZY_LOADING_ENABLED=true

# PERFORMANCE_MONITORING_ENABLED=true
# PERFORMANCE_LOGGING_ENABLED=false
# SLOW_QUERY_THRESHOLD=1000
# MEMORY_LIMIT_THRESHOLD=128
# RESPONSE_TIME_THRESHOLD=2000

# CDN_ENABLED=false
# CDN_URL=
