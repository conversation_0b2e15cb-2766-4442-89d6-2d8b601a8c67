<?php

namespace App\Http\Controllers;

use App\Models\Profile;
use App\Models\Setting;
use App\Services\SEOService;
use Inertia\Inertia;

class ProfileController extends Controller
{
    public function __construct(
        private SEOService $seoService
    ) {}

    public function index()
    {
        // Get profile data from database instead of hardcoded data
        $history = Profile::getHistory()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->getImageUrl(),
                'order' => $profile->order,
            ];
        });

        $visionMission = Profile::getVisionMission()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->getImageUrl(),
                'order' => $profile->order,
            ];
        });

        $demographics = Profile::getDemographics()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->getImageUrl(),
                'order' => $profile->order,
            ];
        });

        $geography = Profile::getGeography()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->getImageUrl(),
                'order' => $profile->order,
            ];
        });

        $organization = Profile::getOrganization()->map(function ($profile) {
            return [
                'id' => $profile->id,
                'section' => $profile->section,
                'title' => $profile->title,
                'content' => $profile->content,
                'image' => $profile->image,
                'order' => $profile->order,
            ];
        });

        // Get settings for dynamic content
        $villageProfile = Setting::get('village.profile', [
            'name' => 'Desa Lemah Duhur',
            'district' => 'Kecamatan Caringin',
            'regency' => 'Kabupaten Bogor',
            'province' => 'Jawa Barat',
            'established_year' => '1910-1920',
            'area' => 'Dataran tinggi pegunungan',
            'population' => 'Data akan diperbarui sesuai sensus terbaru',
        ]);

        $contactInfo = Setting::get('village.contact_info', [
            'phone' => '',
            'whatsapp' => '',
            'email' => '<EMAIL>',
            'address' => 'Desa Lemah Duhur, Kecamatan Caringin, Kabupaten Bogor, Jawa Barat',
            'postal_code' => '16730',
            'maps_link' => 'https://maps.app.goo.gl/F2SWrp4QBYBJXiG6A',
        ]);

        $operatingHours = Setting::get('village.operating_hours', [
            'weekdays' => 'Senin - Jumat: 08:00 - 15:00 WIB',
            'saturday' => 'Sabtu: 08:00 - 12:00 WIB',
            'sunday' => 'Minggu: Tutup',
            'break' => 'Istirahat: 12:00 - 13:00 WIB',
            'holidays' => 'Hari libur nasional: Tutup',
        ]);

        // Get all settings for frontend access
        $settings = [
            'village.contact_info' => $contactInfo,
            'village.profile' => $villageProfile,
            'village.operating_hours' => $operatingHours,
            'village.emergency_contacts' => Setting::get('village.emergency_contacts', []),
            'village.history' => Setting::get('village.history', []),
            'village.service_settings' => Setting::get('village.service_settings', []),
        ];

        // Generate SEO meta tags
        $seoMeta = $this->seoService->generateMetaTags('profile');
        $structuredData = $this->seoService->generateStructuredData('profile');

        // Combine SEO data as expected by tests
        $seo = array_merge($seoMeta, [
            'og_title' => $seoMeta['title'],
            'og_description' => $seoMeta['description'],
            'og_type' => $seoMeta['type'],
            'og_url' => $seoMeta['url'],
            'og_image' => $seoMeta['image'] ?? null,
            'twitter_card' => 'summary_large_image',
            'twitter_title' => $seoMeta['title'],
            'twitter_description' => $seoMeta['description'],
            'twitter_image' => $seoMeta['image'] ?? null,
            'canonical' => $seoMeta['url'],
            'structured_data' => $structuredData,
        ]);

        return Inertia::render('Public/Profile', [
            'history' => $history,
            'visionMission' => $visionMission,
            'demographics' => $demographics,
            'geography' => $geography,
            'organization' => $organization,
            'settings' => $settings,
            'seo' => $seo,
            'seoMeta' => [
                'title' => "Profil {$villageProfile['name']} - Sejarah, Visi & Misi",
                'description' => "Kenali profil lengkap {$villageProfile['name']}: sejarah, visi, misi, demografi, geografi, dan infrastruktur desa yang berkembang di {$villageProfile['district']}.",
                'keywords' => ['profil desa', 'lemah duhur', 'caringin', 'bogor', 'sejarah desa', 'visi misi'],
                'image' => asset('images/profile-og.jpg'),
                'url' => url('/profil'),
                'type' => 'website',
                'siteName' => $villageProfile['name'],
                'locale' => 'id_ID',
            ],
            'structuredData' => $structuredData,
        ]);
    }
}
