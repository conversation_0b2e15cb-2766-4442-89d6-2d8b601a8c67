import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { usePage } from '@inertiajs/react';
import { AlertTriangle, CheckCircle, Info, X, XCircle } from 'lucide-react';
import React, { useEffect } from 'react';
import { useToast } from './ToastProvider';

interface FlashData {
    success?: string;
    error?: string;
    warning?: string;
    info?: string;
    message?: string;
}

interface PagePropsWithFlash {
    flash: FlashData;
    [key: string]: unknown;
}

const FlashMessages: React.FC = () => {
    const page = usePage();
    const flash = (page.props as unknown as PagePropsWithFlash).flash;
    const { toasts, removeToast } = useToast();

    // Handle Laravel flash messages
    useEffect(() => {
        if (flash.success) {
            // Don't add to toast, just show as temporary alert
        }
        if (flash.error) {
            // Don't add to toast, just show as temporary alert
        }
        if (flash.warning) {
            // Don't add to toast, just show as temporary alert
        }
        if (flash.info) {
            // Don't add to toast, just show as temporary alert
        }
        if (flash.message) {
            // Don't add to toast, just show as temporary alert
        }
    }, [flash]);

    const getIcon = (type: string) => {
        switch (type) {
            case 'success':
                return <CheckCircle className="text-green-600" />;
            case 'error':
                return <XCircle className="text-red-600" />;
            case 'warning':
                return <AlertTriangle className="text-yellow-600" />;
            case 'info':
                return <Info className="text-blue-600" />;
            default:
                return <Info className="text-blue-600" />;
        }
    };

    const getAlertVariant = (type: string): 'default' | 'destructive' => {
        return type === 'error' ? 'destructive' : 'default';
    };

    const getAlertStyles = (type: string) => {
        switch (type) {
            case 'success':
                return 'border-green-200 bg-green-50 text-green-800';
            case 'error':
                return 'border-red-200 bg-red-50 text-red-800';
            case 'warning':
                return 'border-yellow-200 bg-yellow-50 text-yellow-800';
            case 'info':
                return 'border-blue-200 bg-blue-50 text-blue-800';
            default:
                return 'border-blue-200 bg-blue-50 text-blue-800';
        }
    };

    // Show Laravel flash messages
    const flashMessages = [
        { type: 'success', message: flash.success, title: 'Berhasil' },
        { type: 'error', message: flash.error, title: 'Kesalahan' },
        { type: 'warning', message: flash.warning, title: 'Peringatan' },
        { type: 'info', message: flash.info, title: 'Informasi' },
        { type: 'info', message: flash.message, title: 'Pesan' },
    ].filter((item) => item.message);

    if (flashMessages.length === 0 && toasts.length === 0) {
        return null;
    }

    return (
        <>
            {/* Laravel Flash Messages - Fixed position at top */}
            {flashMessages.length > 0 && (
                <div className="fixed top-4 left-1/2 z-50 w-full max-w-md -translate-x-1/2 transform px-4">
                    <div className="space-y-2">
                        {flashMessages.map((item, index) => (
                            <Alert
                                key={index}
                                variant={getAlertVariant(item.type)}
                                className={`${getAlertStyles(item.type)} shadow-lg duration-300 animate-in slide-in-from-top-2`}
                            >
                                {getIcon(item.type)}
                                <AlertTitle>{item.title}</AlertTitle>
                                <AlertDescription>{item.message}</AlertDescription>
                            </Alert>
                        ))}
                    </div>
                </div>
            )}

            {/* Toast Messages - Fixed position at top right */}
            {toasts.length > 0 && (
                <div className="fixed top-4 right-4 z-50 w-full max-w-sm space-y-2">
                    {toasts.map((toast) => (
                        <Alert
                            key={toast.id}
                            variant={getAlertVariant(toast.type)}
                            className={`${getAlertStyles(toast.type)} relative shadow-lg duration-300 animate-in slide-in-from-right-2`}
                        >
                            {getIcon(toast.type)}
                            <div className="flex-1">
                                {toast.title && <AlertTitle>{toast.title}</AlertTitle>}
                                <AlertDescription>{toast.message}</AlertDescription>
                            </div>
                            <button
                                onClick={() => removeToast(toast.id)}
                                className="absolute top-2 right-2 rounded-full p-1 transition-colors hover:bg-black/10"
                                aria-label="Tutup pesan"
                            >
                                <X className="h-4 w-4" />
                            </button>
                        </Alert>
                    ))}
                </div>
            )}
        </>
    );
};

export default FlashMessages;
