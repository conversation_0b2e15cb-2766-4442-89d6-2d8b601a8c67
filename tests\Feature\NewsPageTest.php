<?php

use App\Models\News;

describe('News Page Feature', function () {
    beforeEach(function () {
        // Create test news articles
        News::factory()->published()->create([
            'title' => 'Berita Terbaru Desa',
            'category' => 'pengumuman',
            'published_at' => now()->subDay(),
        ]);

        News::factory()->published()->create([
            'title' => 'Kegiatan Gotong Royong',
            'category' => 'kegiatan',
            'published_at' => now()->subDays(2),
        ]);

        News::factory()->published()->create([
            'title' => 'Pembangunan Jalan Desa',
            'category' => 'pembangunan',
            'published_at' => now()->subDays(3),
        ]);

        // Create unpublished news (should not appear)
        News::factory()->unpublished()->create([
            'title' => 'Berita Belum Publish',
        ]);

        // Create future published news (should not appear)
        News::factory()->futurePublished()->create([
            'title' => 'Berita Masa Depan',
        ]);
    });

    it('displays news page successfully', function () {
        $response = $this->get('/berita');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/News/Index')
        );
    });

    it('shows list of published news', function () {
        $response = $this->get('/berita');

        $response->assertInertia(fn ($page) => $page->has('news')
            ->has('news.data')
            ->where('news.data', fn ($news) => count($news) === 3 // Only published news
            )
        );
    });

    it('orders news by latest published date', function () {
        $response = $this->get('/berita');

        $response->assertInertia(fn ($page) => $page->where('news.data', function ($news) {
            return $news[0]['title'] === 'Berita Terbaru Desa'; // Latest first
        })
        );
    });

    it('includes pagination', function () {
        // Create more news for pagination
        News::factory()->published()->count(20)->create();

        $response = $this->get('/berita');

        $response->assertInertia(fn ($page) => $page->has('news.links')
            ->has('news.current_page')
            ->has('news.last_page')
        );
    });

    it('filters by category', function () {
        $response = $this->get('/berita?category=kegiatan');

        $response->assertInertia(fn ($page) => $page->where('news.data', function ($news) {
            foreach ($news as $article) {
                if ($article['category'] !== 'kegiatan') {
                    return false;
                }
            }

            return true;
        })
        );
    });

    it('includes available categories', function () {
        $response = $this->get('/berita');

        $response->assertInertia(function ($page) {
            return $page->has('categories')
                ->where('categories', function ($categories) {
                    // Handle both array and Collection
                    $categoriesArray = is_array($categories) ? $categories : $categories->toArray();

                    return in_array('pengumuman', $categoriesArray) &&
                           in_array('kegiatan', $categoriesArray) &&
                           in_array('pembangunan', $categoriesArray);
                });
        });
    });

    it('includes SEO meta tags', function () {
        $response = $this->get('/berita');

        $response->assertInertia(fn ($page) => $page->has('seo')
            ->has('seo.title')
            ->has('seo.description')
            ->where('seo.title', fn ($title) => str_contains($title, 'Berita')
            )
        );
    });

    describe('News Detail Page', function () {
        it('displays individual news article', function () {
            $news = News::factory()->published()->create([
                'title' => 'Detail Berita Test',
                'slug' => 'detail-berita-test',
                'content' => 'Konten lengkap berita test',
            ]);

            $response = $this->get("/berita/{$news->slug}");

            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page->component('Public/News/Show')
                ->has('news')
                ->where('news.title', 'Detail Berita Test')
            );
        });

        it('shows 404 for unpublished news', function () {
            $news = News::factory()->unpublished()->create([
                'slug' => 'unpublished-news',
            ]);

            $response = $this->get("/berita/{$news->slug}");

            $response->assertStatus(404);
        });

        it('shows 404 for future published news', function () {
            $news = News::factory()->futurePublished()->create([
                'slug' => 'future-news',
            ]);

            $response = $this->get("/berita/{$news->slug}");

            $response->assertStatus(404);
        });

        it('shows 404 for non-existent news', function () {
            $response = $this->get('/berita/non-existent-slug');

            $response->assertStatus(404);
        });

        it('includes related news', function () {
            $mainNews = News::factory()->published()->create([
                'category' => 'kegiatan',
                'slug' => 'main-news',
            ]);

            // Create related news in same category
            News::factory()->published()->count(3)->create([
                'category' => 'kegiatan',
            ]);

            $response = $this->get("/berita/{$mainNews->slug}");

            $response->assertInertia(fn ($page) => $page->has('relatedNews')
                ->where('relatedNews', fn ($related) => count($related) > 0
                )
            );
        });

        it('includes proper SEO meta tags for article', function () {
            $news = News::factory()->published()->withMeta()->create([
                'slug' => 'seo-test-news',
                'meta_title' => 'Custom Meta Title',
                'meta_description' => 'Custom meta description',
            ]);

            $response = $this->get("/berita/{$news->slug}");

            $response->assertInertia(fn ($page) => $page->has('seo')
                ->where('seo.title', 'Custom Meta Title')
                ->where('seo.description', 'Custom meta description')
            );
        });

        it('includes featured image in meta tags', function () {
            $news = News::factory()->published()->withFeaturedImage()->create([
                'slug' => 'image-test-news',
            ]);

            $response = $this->get("/berita/{$news->slug}");

            $response->assertInertia(fn ($page) => $page->has('seo.image')
            );
        });
    });

    describe('Search Functionality', function () {
        it('can search news by title', function () {
            $response = $this->get('/berita?search=gotong');

            $response->assertInertia(fn ($page) => $page->where('news.data', function ($news) {
                foreach ($news as $article) {
                    if (! str_contains(strtolower($article['title']), 'gotong')) {
                        return false;
                    }
                }

                return count($news) > 0;
            })
            );
        });

        it('returns empty results for non-matching search', function () {
            $response = $this->get('/berita?search=nonexistent');

            $response->assertInertia(fn ($page) => $page->where('news.data', fn ($news) => count($news) === 0
            )
            );
        });

        it('can combine search with category filter', function () {
            $response = $this->get('/berita?search=kegiatan&category=kegiatan');

            $response->assertInertia(fn ($page) => $page->where('news.data', function ($news) {
                foreach ($news as $article) {
                    if ($article['category'] !== 'kegiatan' ||
                        ! str_contains(strtolower($article['title']), 'kegiatan')) {
                        return false;
                    }
                }

                return true;
            })
            );
        });
    });

    describe('Performance', function () {
        it('loads news list efficiently', function () {
            \DB::enableQueryLog();

            $response = $this->get('/berita');

            $queries = \DB::getQueryLog();

            $response->assertStatus(200);
            expect(count($queries))->toBeLessThan(5);
        });

        it('handles large number of news articles', function () {
            News::factory()->published()->count(100)->create();

            $response = $this->get('/berita');

            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page->has('news.data')
                ->where('news.data', fn ($news) => count($news) <= 10 // Should be paginated
                )
            );
        });
    });

    describe('Content Validation', function () {
        it('contains Indonesian content', function () {
            $response = $this->get('/berita');

            $response->assertInertia(fn ($page) => $page->where('news.data', function ($news) {
                foreach ($news as $article) {
                    if (str_contains($article['title'], 'Desa') ||
                        str_contains($article['title'], 'Kegiatan')) {
                        return true;
                    }
                }

                return false;
            })
            );
        });
    });
});
