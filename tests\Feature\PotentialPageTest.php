<?php

use App\Models\Potential;

describe('Potential Page Feature', function () {
    beforeEach(function () {
        // Create tourism potentials
        Potential::factory()->tourism()->create([
            'name' => 'Air Terjun <PERSON>',
            'description' => 'Air terjun yang indah dengan ketinggian 50 meter',
            'is_featured' => true,
        ]);

        Potential::factory()->tourism()->create([
            'name' => 'Pemandian Air Panas',
            'description' => 'Pemandian air panas alami yang menyegarkan',
            'is_featured' => false,
        ]);

        // Create UMKM potentials
        Potential::factory()->umkm()->create([
            'name' => 'Keripik Singkong Bu Sari',
            'description' => 'Keripik singkong dengan berbagai rasa',
            'is_featured' => true,
        ]);

        Potential::factory()->umkm()->create([
            'name' => 'Kopi Robusta Lemah Duhur',
            'description' => 'Kopi robusta berkualitas tinggi dari kebun lokal',
            'is_featured' => false,
        ]);
    });

    it('displays potential page successfully', function () {
        $response = $this->get('/potensi');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/Potential/Index')
        );
    });

    it('shows both tourism and UMKM potentials', function () {
        $response = $this->get('/potensi');

        $response->assertInertia(fn ($page) => $page->has('tourism')
            ->has('umkm')
            ->where('tourism', fn ($tourism) => count($tourism) === 2)
            ->where('umkm', fn ($umkm) => count($umkm) === 2)
        );
    });

    it('separates tourism and UMKM correctly', function () {
        $response = $this->get('/potensi');

        $response->assertInertia(fn ($page) => $page->where('tourism', function ($tourism) {
            foreach ($tourism as $item) {
                if ($item['type'] !== 'tourism') {
                    return false;
                }
            }

            return true;
        })
            ->where('umkm', function ($umkm) {
                foreach ($umkm as $item) {
                    if ($item['type'] !== 'umkm') {
                        return false;
                    }
                }

                return true;
            })
        );
    });

    it('shows featured potentials prominently', function () {
        $response = $this->get('/potensi');

        $response->assertInertia(fn ($page) => $page->has('featuredPotentials')
            ->where('featuredPotentials', fn ($featured) => count($featured) === 2 // Both featured items
            )
        );
    });

    it('includes contact information for each potential', function () {
        $response = $this->get('/potensi');

        $response->assertInertia(fn ($page) => $page->where('tourism', function ($tourism) {
            foreach ($tourism as $item) {
                if (! isset($item['contact_list']) || empty($item['contact_list'])) {
                    return false;
                }
            }

            return true;
        })
        );
    });

    it('includes location information', function () {
        $response = $this->get('/potensi');

        $response->assertInertia(fn ($page) => $page->where('tourism', function ($tourism) {
            foreach ($tourism as $item) {
                if (! isset($item['location'])) {
                    return false;
                }
            }

            return true;
        })
        );
    });

    it('includes SEO meta tags', function () {
        $response = $this->get('/potensi');

        $response->assertInertia(fn ($page) => $page->has('seo')
            ->has('seo.title')
            ->has('seo.description')
            ->where('seo.title', fn ($title) => str_contains($title, 'Potensi')
            )
        );
    });

    describe('Filter by Type', function () {
        it('can filter tourism only', function () {
            $response = $this->get('/potensi?type=tourism');

            $response->assertInertia(fn ($page) => $page->has('potentials')
                ->where('potentials', function ($potentials) {
                    foreach ($potentials as $item) {
                        if ($item['type'] !== 'tourism') {
                            return false;
                        }
                    }

                    return count($potentials) === 2;
                })
            );
        });

        it('can filter UMKM only', function () {
            $response = $this->get('/potensi?type=umkm');

            $response->assertInertia(fn ($page) => $page->has('potentials')
                ->where('potentials', function ($potentials) {
                    foreach ($potentials as $item) {
                        if ($item['type'] !== 'umkm') {
                            return false;
                        }
                    }

                    return count($potentials) === 2;
                })
            );
        });
    });

    describe('Potential Detail', function () {
        it('can view individual potential details', function () {
            $potential = Potential::factory()->tourism()->withImages()->create([
                'name' => 'Detail Tourism Test',
            ]);

            $response = $this->get("/potensi/{$potential->id}");

            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page->component('Public/Potential/Show')
                ->has('potential')
                ->where('potential.name', 'Detail Tourism Test')
            );
        });

        it('shows image gallery when available', function () {
            $potential = Potential::factory()->tourism()->withImages()->create();

            $response = $this->get("/potensi/{$potential->id}");

            $response->assertInertia(fn ($page) => $page->where('potential.image_list', fn ($images) => count($images) > 0
            )
            );
        });

        it('shows contact information for follow-up', function () {
            $potential = Potential::factory()->umkm()->create([
                'contact_info' => [
                    'phone' => '08123456789',
                    'email' => '<EMAIL>',
                    'person' => 'John Doe',
                ],
            ]);

            $response = $this->get("/potensi/{$potential->id}");

            $response->assertInertia(fn ($page) => $page->where('potential.contact_list', function ($contact) {
                return isset($contact['phone']) &&
                       isset($contact['email']) &&
                       isset($contact['person']);
            })
            );
        });

        it('shows 404 for non-existent potential', function () {
            $response = $this->get('/potensi/999');

            $response->assertStatus(404);
        });

        it('includes proper SEO for individual potential', function () {
            $potential = Potential::factory()->tourism()->create([
                'name' => 'SEO Test Tourism',
                'description' => 'Description for SEO test',
            ]);

            $response = $this->get("/potensi/{$potential->id}");

            $response->assertInertia(fn ($page) => $page->has('seo')
                ->where('seo.title', fn ($title) => str_contains($title, 'SEO Test Tourism')
                )
            );
        });
    });

    describe('Search Functionality', function () {
        it('can search potentials by name', function () {
            $response = $this->get('/potensi?search=air terjun');

            $response->assertInertia(fn ($page) => $page->where('potentials', function ($potentials) {
                foreach ($potentials as $item) {
                    if (! str_contains(strtolower($item['name']), 'air terjun')) {
                        return false;
                    }
                }

                return count($potentials) > 0;
            })
            );
        });

        it('can search by description', function () {
            $response = $this->get('/potensi?search=keripik');

            $response->assertInertia(fn ($page) => $page->where('potentials', function ($potentials) {
                foreach ($potentials as $item) {
                    if (! str_contains(strtolower($item['name']), 'keripik')) {
                        return false;
                    }
                }

                return count($potentials) > 0;
            })
            );
        });

        it('returns empty results for non-matching search', function () {
            $response = $this->get('/potensi?search=nonexistent');

            $response->assertInertia(fn ($page) => $page->where('potentials', fn ($potentials) => count($potentials) === 0
            )
            );
        });
    });

    describe('Performance', function () {
        it('loads potentials efficiently', function () {
            \DB::enableQueryLog();

            $response = $this->get('/potensi');

            $queries = \DB::getQueryLog();

            $response->assertStatus(200);
            expect(count($queries))->toBeLessThan(6);
        });

        it('handles large number of potentials', function () {
            Potential::factory()->tourism()->count(50)->create();
            Potential::factory()->umkm()->count(50)->create();

            $response = $this->get('/potensi');

            $response->assertStatus(200);
            // Should still load efficiently
        });
    });

    describe('Content Validation', function () {
        it('contains Indonesian content', function () {
            $response = $this->get('/potensi');

            $response->assertInertia(fn ($page) => $page->where('tourism', function ($tourism) {
                foreach ($tourism as $item) {
                    if (str_contains($item['name'], 'Air Terjun') ||
                        str_contains($item['name'], 'Lemah Duhur')) {
                        return true;
                    }
                }

                return false;
            })
            );
        });

        it('shows correct type names in Indonesian', function () {
            $response = $this->get('/potensi');

            $response->assertInertia(fn ($page) => $page->where('tourism', function ($tourism) {
                foreach ($tourism as $item) {
                    if ($item['type_name'] !== 'Wisata') {
                        return false;
                    }
                }

                return true;
            })
                ->where('umkm', function ($umkm) {
                    foreach ($umkm as $item) {
                        if ($item['type_name'] !== 'UMKM') {
                            return false;
                        }
                    }

                    return true;
                })
            );
        });
    });

    describe('Map Integration', function () {
        it('includes location data for mapping', function () {
            $response = $this->get('/potensi');

            $response->assertInertia(fn ($page) => $page->has('mapData')
            );
        });
    });
});
