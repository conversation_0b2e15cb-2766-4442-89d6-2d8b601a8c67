<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class SettingController extends Controller
{
    /**
     * Display the admin settings page.
     */
    public function index(): Response
    {
        // Get all current settings organized by category
        $settings = [
            'village.contact_info' => Setting::get('village.contact_info', [
                'phone' => '',
                'whatsapp' => '',
                'email' => '',
                'address' => '',
                'postal_code' => '',
                'maps_link' => '',
            ]),
            'village.profile' => Setting::get('village.profile', [
                'name' => '',
                'district' => '',
                'regency' => '',
                'province' => '',
                'established_year' => '',
                'area' => '',
                'population' => '',
            ]),
            'village.operating_hours' => Setting::get('village.operating_hours', [
                'weekdays' => '',
                'saturday' => '',
                'sunday' => '',
                'break' => '',
                'holidays' => '',
            ]),
            'village.emergency_contacts' => Setting::get('village.emergency_contacts', [
                'village_head' => ['title' => '', 'phone' => ''],
                'village_secretary' => ['title' => '', 'phone' => ''],
                'security' => ['title' => '', 'phone' => ''],
                'health_center' => ['title' => '', 'phone' => ''],
            ]),
            'village.service_settings' => Setting::get('village.service_settings', [
                'online_services_available' => false,
                'mobile_optimized' => false,
                'emergency_contact_available' => false,
                'multilingual_support' => false,
                'digital_signature_enabled' => false,
            ]),
        ];

        return Inertia::render('Admin/Settings/Index', [
            'settings' => $settings,
        ]);
    }

    /**
     * Update the settings.
     */
    public function update(\App\Http\Requests\Settings\UpdateSettingsRequest $request): RedirectResponse
    {
        $validated = $request->validated();

        // Update each setting category
        foreach ($validated['village'] as $category => $data) {
            Setting::set("village.{$category}", $data);
        }

        return redirect()->back()->with('message', 'Pengaturan diperbarui');
    }
}
