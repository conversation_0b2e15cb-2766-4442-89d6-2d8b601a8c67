<?php

namespace Tests\Feature\Admin;

use App\Models\Setting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SettingsTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;

    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);

        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Get valid test data in flattened format
     */
    private function getValidTestData(array $overrides = []): array
    {
        $defaultData = [
            'village_contact_info_phone' => '(0251) 8240123',
            'village_contact_info_whatsapp' => '081234567890',
            'village_contact_info_email' => '<EMAIL>',
            'village_contact_info_address' => 'Test address that is long enough',
            'village_contact_info_postal_code' => '16730',
            'village_contact_info_maps_link' => 'https://maps.app.goo.gl/F2SWrp4QBYBJXiG6A',
            'village_profile_name' => 'Desa Test',
            'village_profile_district' => 'Test District',
            'village_profile_regency' => 'Test Regency',
            'village_profile_province' => 'Test Province',
            'village_profile_established_year' => '1910-1920',
            'village_profile_area' => 'Test area',
            'village_profile_population' => '5,000 jiwa',
            'village_operating_hours_weekdays' => 'Test weekdays',
            'village_operating_hours_saturday' => 'Test saturday',
            'village_operating_hours_sunday' => 'Test sunday',
            'village_operating_hours_break' => 'Test break',
            'village_operating_hours_holidays' => 'Test holidays',
            'village_emergency_contacts_village_head_title' => 'Kepala Desa',
            'village_emergency_contacts_village_head_phone' => '081234567890',
            'village_emergency_contacts_village_secretary_title' => 'Sekretaris Desa',
            'village_emergency_contacts_village_secretary_phone' => '081234567890',
            'village_emergency_contacts_security_title' => 'Keamanan Desa',
            'village_emergency_contacts_security_phone' => '081234567890',
            'village_emergency_contacts_health_center_title' => 'Puskesmas Test',
            'village_emergency_contacts_health_center_phone' => '(0251) 8240456',
        ];

        return array_merge($defaultData, $overrides);
    }

    public function test_admin_can_access_settings_page(): void
    {
        // Seed the settings
        $this->seed(\Database\Seeders\SettingSeeder::class);

        // Login as admin
        $this->actingAs($this->admin);

        // Access admin settings page
        $response = $this->get('/admin/settings');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('Admin/Settings/Index')
            ->has('settings', 5) // Should have 5 setting categories
        );

        // Verify the settings structure by checking the response data
        $settings = $response->viewData('page')['props']['settings'];
        $this->assertArrayHasKey('village.contact_info', $settings);
        $this->assertArrayHasKey('village.profile', $settings);
        $this->assertArrayHasKey('village.operating_hours', $settings);
        $this->assertArrayHasKey('village.emergency_contacts', $settings);
        $this->assertArrayHasKey('village.service_settings', $settings);
    }

    public function test_regular_user_cannot_access_settings_page(): void
    {
        // Login as regular user
        $this->actingAs($this->regularUser);

        // Try to access admin settings page
        $response = $this->get('/admin/settings');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_settings_page(): void
    {
        // Try to access admin settings page without login
        $response = $this->get('/admin/settings');

        $response->assertRedirect('/login');
    }

    public function test_admin_can_update_settings_with_valid_data(): void
    {
        $this->actingAs($this->admin);

        $validData = $this->getValidTestData([
            'village_contact_info_email' => '<EMAIL>',
            'village_contact_info_address' => 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat',
            'village_profile_name' => 'Desa Lemah Duhur',
            'village_profile_district' => 'Kecamatan Caringin',
            'village_profile_regency' => 'Kabupaten Bogor',
            'village_profile_province' => 'Jawa Barat',
            'village_profile_area' => 'Dataran tinggi pegunungan',
            'village_operating_hours_weekdays' => 'Senin - Jumat: 08:00 - 15:00 WIB',
            'village_operating_hours_saturday' => 'Sabtu: 08:00 - 12:00 WIB',
            'village_operating_hours_sunday' => 'Minggu: Tutup',
            'village_operating_hours_break' => 'Istirahat: 12:00 - 13:00 WIB',
            'village_operating_hours_holidays' => 'Hari libur nasional: Tutup',
            'village_emergency_contacts_village_head_phone' => '081234567891',
            'village_emergency_contacts_village_secretary_phone' => '081234567892',
            'village_emergency_contacts_security_title' => 'Keamanan Desa (Hansip)',
            'village_emergency_contacts_security_phone' => '081234567893',
            'village_emergency_contacts_health_center_title' => 'Puskesmas Caringin',
        ]);

        $response = $this->put('/admin/settings', $validData);

        $response->assertRedirect();
        $response->assertSessionHas('message', 'Pengaturan diperbarui');
    }

    public function test_regular_user_cannot_update_settings(): void
    {
        $this->actingAs($this->regularUser);

        $data = $this->getValidTestData();

        $response = $this->put('/admin/settings', $data);

        $response->assertStatus(403);
    }

    public function test_guest_cannot_update_settings(): void
    {
        $data = $this->getValidTestData();

        $response = $this->put('/admin/settings', $data);

        $response->assertRedirect('/login');
    }

    public function test_validation_fails_with_invalid_phone_number(): void
    {
        $this->actingAs($this->admin);

        $invalidData = $this->getValidTestData([
            'village_contact_info_phone' => 'invalid-phone',
        ]);

        $response = $this->put('/admin/settings', $invalidData);

        $response->assertSessionHasErrors(['village.contact_info.phone']);
    }

    public function test_validation_fails_with_invalid_email(): void
    {
        $this->actingAs($this->admin);

        $invalidData = $this->getValidTestData([
            'village_contact_info_email' => 'invalid-email',
        ]);

        $response = $this->put('/admin/settings', $invalidData);

        $response->assertSessionHasErrors(['village.contact_info.email']);
    }

    public function test_validation_fails_with_invalid_postal_code(): void
    {
        $this->actingAs($this->admin);

        $invalidData = $this->getValidTestData([
            'village_contact_info_postal_code' => '1673', // Invalid: should be 5 digits
        ]);

        $response = $this->put('/admin/settings', $invalidData);

        $response->assertSessionHasErrors(['village.contact_info.postal_code']);
    }

    public function test_validation_fails_with_invalid_whatsapp_number(): void
    {
        $this->actingAs($this->admin);

        $invalidData = $this->getValidTestData([
            'village_contact_info_whatsapp' => '071234567890', // Invalid: should start with 08 or +62
        ]);

        $response = $this->put('/admin/settings', $invalidData);

        $response->assertSessionHasErrors(['village.contact_info.whatsapp']);
    }

    public function test_validation_requires_mandatory_fields(): void
    {
        $this->actingAs($this->admin);

        $incompleteData = [
            'village_contact_info_maps_link' => 'https://maps.app.goo.gl/F2SWrp4QBYBJXiG6A',
            // Missing all other required fields
        ];

        $response = $this->put('/admin/settings', $incompleteData);

        $response->assertSessionHasErrors([
            'village.contact_info.phone',
            'village.contact_info.whatsapp',
            'village.contact_info.email',
            'village.contact_info.address',
            'village.contact_info.postal_code',
        ]);
    }

    public function test_validation_fails_with_short_address(): void
    {
        $this->actingAs($this->admin);

        $invalidData = $this->getValidTestData([
            'village_contact_info_address' => 'Short', // Too short (min 10 characters)
        ]);

        $response = $this->put('/admin/settings', $invalidData);

        $response->assertSessionHasErrors(['village.contact_info.address']);
    }

    public function test_validation_fails_with_short_village_name(): void
    {
        $this->actingAs($this->admin);

        $invalidData = $this->getValidTestData([
            'village_profile_name' => 'AB', // Too short (min 3 characters)
        ]);

        $response = $this->put('/admin/settings', $invalidData);

        $response->assertSessionHasErrors(['village.profile.name']);
    }

    public function test_validation_accepts_valid_indonesian_phone_formats(): void
    {
        $this->actingAs($this->admin);

        $validPhoneFormats = [
            '(0251) 8240123',
            '+6281234567890',
            '081234567890',
        ];

        foreach ($validPhoneFormats as $phone) {
            $validData = $this->getValidTestData([
                'village_contact_info_phone' => $phone,
            ]);

            $response = $this->put('/admin/settings', $validData);

            // Should not have validation errors for phone field
            $this->assertFalse(
                session()->has('errors') && session('errors')->has('village.contact_info.phone'),
                "Phone format {$phone} should be valid"
            );
        }
    }

    public function test_validation_accepts_valid_whatsapp_formats(): void
    {
        $this->actingAs($this->admin);

        $validWhatsAppFormats = [
            '081234567890',
            '082123456789',
            '083123456789',
            '085123456789',
            '087123456789',
            '088123456789',
            '089123456789',
            '+6281234567890',
        ];

        foreach ($validWhatsAppFormats as $whatsapp) {
            $validData = $this->getValidTestData([
                'village_contact_info_whatsapp' => $whatsapp,
            ]);

            $response = $this->put('/admin/settings', $validData);

            // Should not have validation errors for whatsapp field
            $this->assertFalse(
                session()->has('errors') && session('errors')->has('village.contact_info.whatsapp'),
                "WhatsApp format {$whatsapp} should be valid"
            );
        }
    }

    public function test_validation_error_messages_are_in_indonesian(): void
    {
        $this->actingAs($this->admin);

        $invalidData = [
            'village_contact_info_phone' => '', // Empty required field
            'village_contact_info_email' => 'invalid-email',
            'village_contact_info_postal_code' => '123', // Invalid format
        ];

        $response = $this->put('/admin/settings', $invalidData);

        $response->assertSessionHasErrors();

        $errors = session('errors');

        // Check that error messages are in Indonesian
        $this->assertStringContainsString('wajib diisi', $errors->first('village.contact_info.phone'));
        $this->assertStringContainsString('tidak valid', $errors->first('village.contact_info.email'));
        $this->assertStringContainsString('5 digit', $errors->first('village.contact_info.postal_code'));
    }

    public function test_settings_page_displays_current_values(): void
    {
        $this->actingAs($this->admin);

        // Set some test settings
        Setting::set('village.contact_info', [
            'phone' => '(0251) 8240123',
            'email' => '<EMAIL>',
        ]);

        $response = $this->get('/admin/settings');

        $response->assertInertia(fn ($page) => $page
            ->component('Admin/Settings/Index')
        );

        // Verify the settings values
        $settings = $response->viewData('page')['props']['settings'];
        $this->assertEquals('(0251) 8240123', $settings['village.contact_info']['phone']);
        $this->assertEquals('<EMAIL>', $settings['village.contact_info']['email']);
    }

    public function test_emergency_contacts_are_saved_and_displayed(): void
    {
        $this->actingAs($this->admin);

        $testData = $this->getValidTestData([
            'village_emergency_contacts_village_head_title' => 'Kepala Desa Baru',
            'village_emergency_contacts_village_head_phone' => '081999888777',
        ]);

        $response = $this->put('/admin/settings', $testData);
        $response->assertRedirect();
        $response->assertSessionHas('message', 'Pengaturan diperbarui');

        // Verify emergency contacts were saved
        $savedEmergencyContacts = Setting::get('village.emergency_contacts');
        $this->assertNotNull($savedEmergencyContacts, 'Emergency contacts should be saved');
        $this->assertArrayHasKey('village_head', $savedEmergencyContacts);
        $this->assertEquals('Kepala Desa Baru', $savedEmergencyContacts['village_head']['title']);
        $this->assertEquals('081999888777', $savedEmergencyContacts['village_head']['phone']);

        // Verify they appear on the settings page
        $response = $this->get('/admin/settings');
        $settings = $response->viewData('page')['props']['settings'];
        $this->assertEquals('Kepala Desa Baru', $settings['village.emergency_contacts']['village_head']['title']);
        $this->assertEquals('081999888777', $settings['village.emergency_contacts']['village_head']['phone']);
    }
}
